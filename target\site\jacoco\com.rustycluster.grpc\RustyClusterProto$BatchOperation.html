<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterProto.BatchOperation</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">com.rustycluster.grpc</a> &gt; <span class="el_class">RustyClusterProto.BatchOperation</span></div><h1>RustyClusterProto.BatchOperation</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">672 of 816</td><td class="ctr2">17%</td><td class="bar">97 of 104</td><td class="ctr2">6%</td><td class="ctr1">86</td><td class="ctr2">99</td><td class="ctr1">165</td><td class="ctr2">205</td><td class="ctr1">34</td><td class="ctr2">47</td></tr></tfoot><tbody><tr><td id="a19"><a href="RustyClusterProto.java.html#L19749" class="el_method">hashCode()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="138" alt="138"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="10" alt="10"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h1">28</td><td class="ctr2" id="i1">28</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="RustyClusterProto.java.html#L19709" class="el_method">equals(Object)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="106" height="10" title="122" alt="122"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="36" alt="36"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f0">19</td><td class="ctr2" id="g0">19</td><td class="ctr1" id="h0">29</td><td class="ctr2" id="i0">29</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a13"><a href="RustyClusterProto.java.html#L19673" class="el_method">getSerializedSize()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="89" height="10" title="103" alt="103"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="16" alt="16"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h2">24</td><td class="ctr2" id="i2">24</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a46"><a href="RustyClusterProto.java.html#L19647" class="el_method">writeTo(CodedOutputStream)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="63" height="10" title="73" alt="73"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="14" alt="14"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h3">16</td><td class="ctr2" id="i3">16</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a9"><a href="RustyClusterProto.java.html#L19442" class="el_method">getKeyBytes()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="18" alt="18"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h4">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a16"><a href="RustyClusterProto.java.html#L19481" class="el_method">getValueBytes()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="18" alt="18"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="RustyClusterProto.java.html#L19540" class="el_method">getFieldBytes()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="18" alt="18"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a8"><a href="RustyClusterProto.java.html#L19424" class="el_method">getKey()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c10">45%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h7">4</td><td class="ctr2" id="i9">7</td><td class="ctr1" id="j34">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a15"><a href="RustyClusterProto.java.html#L19463" class="el_method">getValue()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c11">45%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h8">4</td><td class="ctr2" id="i10">7</td><td class="ctr1" id="j35">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a4"><a href="RustyClusterProto.java.html#L19518" class="el_method">getField()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c12">45%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h9">4</td><td class="ctr2" id="i11">7</td><td class="ctr1" id="j36">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a17"><a href="RustyClusterProto.java.html#L19506" class="el_method">hasField()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a20"><a href="RustyClusterProto.java.html#L19564" class="el_method">hasIntValue()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a18"><a href="RustyClusterProto.java.html#L19591" class="el_method">hasFloatValue()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a21"><a href="RustyClusterProto.java.html#L19618" class="el_method">hasTtl()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a27"><a href="RustyClusterProto.java.html#L19873" class="el_method">newBuilderForType(GeneratedMessageV3.BuilderParent)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="7" alt="7"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h10">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a34"><a href="RustyClusterProto.java.html#L19793" class="el_method">parseFrom(ByteBuffer, ExtensionRegistryLite)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a36"><a href="RustyClusterProto.java.html#L19804" class="el_method">parseFrom(ByteString, ExtensionRegistryLite)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a32"><a href="RustyClusterProto.java.html#L19814" class="el_method">parseFrom(byte[], ExtensionRegistryLite)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a40"><a href="RustyClusterProto.java.html#L19825" class="el_method">parseFrom(InputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a30"><a href="RustyClusterProto.java.html#L19839" class="el_method">parseDelimitedFrom(InputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a38"><a href="RustyClusterProto.java.html#L19852" class="el_method">parseFrom(CodedInputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i16">2</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a45"><a href="RustyClusterProto.java.html#L19866" class="el_method">toBuilder()</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="8" alt="8"/></td><td class="ctr2" id="c9">61%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g15">2</td><td class="ctr1" id="h37">0</td><td class="ctr2" id="i17">2</td><td class="ctr1" id="j37">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a22"><a href="RustyClusterProto.java.html#L19202" class="el_method">internalGetFieldAccessorTable()</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i18">2</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a33"><a href="RustyClusterProto.java.html#L19787" class="el_method">parseFrom(ByteBuffer)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a35"><a href="RustyClusterProto.java.html#L19798" class="el_method">parseFrom(ByteString)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a31"><a href="RustyClusterProto.java.html#L19808" class="el_method">parseFrom(byte[])</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a39"><a href="RustyClusterProto.java.html#L19818" class="el_method">parseFrom(InputStream)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h15">2</td><td class="ctr2" id="i19">2</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a29"><a href="RustyClusterProto.java.html#L19831" class="el_method">parseDelimitedFrom(InputStream)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h16">2</td><td class="ctr2" id="i20">2</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a37"><a href="RustyClusterProto.java.html#L19845" class="el_method">parseFrom(CodedInputStream)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h17">2</td><td class="ctr2" id="i21">2</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a25"><a href="RustyClusterProto.java.html#L19862" class="el_method">newBuilder(RustyClusterProto.BatchOperation)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a23"><a href="RustyClusterProto.java.html#L19636" class="el_method">isInitialized()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="13" alt="13"/></td><td class="ctr2" id="c8">76%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h38">0</td><td class="ctr2" id="i12">5</td><td class="ctr1" id="j38">0</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a28"><a href="RustyClusterProto.java.html#L19191" class="el_method">newInstance(GeneratedMessageV3.UnusedPrivateParameter)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a11"><a href="RustyClusterProto.java.html#L19404" class="el_method">getOperationTypeValue()</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a10"><a href="RustyClusterProto.java.html#L19411" class="el_method">getOperationType()</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="8" alt="8"/></td><td class="ctr2" id="c7">80%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g16">2</td><td class="ctr1" id="h39">0</td><td class="ctr2" id="i22">2</td><td class="ctr1" id="j39">0</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a3"><a href="RustyClusterProto.java.html#L19196" class="el_method">getDescriptor()</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i37">1</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a26"><a href="RustyClusterProto.java.html#L19857" class="el_method">newBuilderForType()</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i38">1</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a1"><a href="RustyClusterProto.java.html#L20622" class="el_method">getDefaultInstance()</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h33">1</td><td class="ctr2" id="i39">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a41"><a href="RustyClusterProto.java.html#L20648" class="el_method">parser()</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h34">1</td><td class="ctr2" id="i40">1</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a12"><a href="RustyClusterProto.java.html#L20653" class="el_method">getParserForType()</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i41">1</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a2"><a href="RustyClusterProto.java.html#L20658" class="el_method">getDefaultInstanceForType()</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h36">1</td><td class="ctr2" id="i42">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a42"><a href="RustyClusterProto.java.html#L19180" class="el_method">RustyClusterProto.BatchOperation()</a></td><td class="bar" id="b40"><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="39" alt="39"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">0</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h40">0</td><td class="ctr2" id="i4">14</td><td class="ctr1" id="j40">0</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a43"><a href="RustyClusterProto.java.html#L19178" class="el_method">RustyClusterProto.BatchOperation(GeneratedMessageV3.Builder)</a></td><td class="bar" id="b41"><img src="../jacoco-resources/greenbar.gif" width="24" height="10" title="28" alt="28"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f41">0</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h41">0</td><td class="ctr2" id="i5">10</td><td class="ctr1" id="j41">0</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a44"><a href="RustyClusterProto.java.html#L20618" class="el_method">static {...}</a></td><td class="bar" id="b42"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f42">0</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h42">0</td><td class="ctr2" id="i23">2</td><td class="ctr1" id="j42">0</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a7"><a href="RustyClusterProto.java.html#L19576" class="el_method">getIntValue()</a></td><td class="bar" id="b43"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f43">0</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h43">0</td><td class="ctr2" id="i43">1</td><td class="ctr1" id="j43">0</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a6"><a href="RustyClusterProto.java.html#L19603" class="el_method">getFloatValue()</a></td><td class="bar" id="b44"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f44">0</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h44">0</td><td class="ctr2" id="i44">1</td><td class="ctr1" id="j44">0</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a14"><a href="RustyClusterProto.java.html#L19630" class="el_method">getTtl()</a></td><td class="bar" id="b45"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f45">0</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h45">0</td><td class="ctr2" id="i45">1</td><td class="ctr1" id="j45">0</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a24"><a href="RustyClusterProto.java.html#L19859" class="el_method">newBuilder()</a></td><td class="bar" id="b46"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">0</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h46">0</td><td class="ctr2" id="i46">1</td><td class="ctr1" id="j46">0</td><td class="ctr2" id="k46">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>