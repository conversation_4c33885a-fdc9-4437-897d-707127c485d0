package com.rustycluster.client.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.Assertions.assertThat;

class NodeRoleTest {

    @Test
    @DisplayName("PRIMARY should have higher priority than SECONDARY and TERTIARY")
    void primaryShouldHaveHigherPriorityThanSecondaryAndTertiary() {
        assertThat(NodeRole.PRIMARY.getPriority()).isLessThan(NodeRole.SECONDARY.getPriority());
        assertThat(NodeRole.PRIMARY.getPriority()).isLessThan(NodeRole.TERTIARY.getPriority());
    }

    @Test
    @DisplayName("SECONDARY should have higher priority than TERTIARY")
    void secondaryShouldHaveHigherPriorityThanTertiary() {
        assertThat(NodeRole.SECONDARY.getPriority()).isLessThan(NodeRole.TERTIARY.getPriority());
    }

    @ParameterizedTest
    @CsvSource({
            "PRIMARY, 1",
            "SECONDARY, 2",
            "TERTIARY, 3"
    })
    @DisplayName("Each role should have the correct priority value")
    void eachRoleShouldHaveCorrectPriorityValue(NodeRole role, int expectedPriority) {
        assertThat(role.getPriority()).isEqualTo(expectedPriority);
    }
}
