<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchOperationBuilder.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client</a> &gt; <span class="el_source">BatchOperationBuilder.java</span></div><h1>BatchOperationBuilder.java</h1><pre class="source lang-java linenums">package com.rustycluster.client;

import com.rustycluster.grpc.RustyClusterProto;

import java.util.ArrayList;
import java.util.List;

/**
 * Builder for creating batch operations.
 */
public class BatchOperationBuilder {
<span class="fc" id="L12">    private final List&lt;RustyClusterProto.BatchOperation&gt; operations = new ArrayList&lt;&gt;();</span>

    /**
     * Create a new BatchOperationBuilder.
     */
<span class="fc" id="L17">    public BatchOperationBuilder() {</span>
<span class="fc" id="L18">    }</span>

    /**
     * Add a SET operation to the batch.
     *
     * @param key   The key
     * @param value The value
     * @return The builder instance
     */
    public BatchOperationBuilder addSet(String key, String value) {
<span class="fc" id="L28">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L29">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SET)</span>
<span class="fc" id="L30">                .setKey(key)</span>
<span class="fc" id="L31">                .setValue(value)</span>
<span class="fc" id="L32">                .build();</span>
<span class="fc" id="L33">        operations.add(operation);</span>
<span class="fc" id="L34">        return this;</span>
    }

    /**
     * Add a DELETE operation to the batch.
     *
     * @param key The key
     * @return The builder instance
     */
    public BatchOperationBuilder addDelete(String key) {
<span class="fc" id="L44">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L45">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.DELETE)</span>
<span class="fc" id="L46">                .setKey(key)</span>
<span class="fc" id="L47">                .build();</span>
<span class="fc" id="L48">        operations.add(operation);</span>
<span class="fc" id="L49">        return this;</span>
    }

    /**
     * Add a SETEX operation to the batch.
     *
     * @param key   The key
     * @param value The value
     * @param ttl   The time-to-live in seconds
     * @return The builder instance
     */
    public BatchOperationBuilder addSetEx(String key, String value, long ttl) {
<span class="fc" id="L61">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L62">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SETEX)</span>
<span class="fc" id="L63">                .setKey(key)</span>
<span class="fc" id="L64">                .setValue(value)</span>
<span class="fc" id="L65">                .setTtl(ttl)</span>
<span class="fc" id="L66">                .build();</span>
<span class="fc" id="L67">        operations.add(operation);</span>
<span class="fc" id="L68">        return this;</span>
    }

    /**
     * Add a SETEXPIRY operation to the batch.
     *
     * @param key The key
     * @param ttl The time-to-live in seconds
     * @return The builder instance
     */
    public BatchOperationBuilder addSetExpiry(String key, long ttl) {
<span class="fc" id="L79">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L80">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SETEXPIRY)</span>
<span class="fc" id="L81">                .setKey(key)</span>
<span class="fc" id="L82">                .setTtl(ttl)</span>
<span class="fc" id="L83">                .build();</span>
<span class="fc" id="L84">        operations.add(operation);</span>
<span class="fc" id="L85">        return this;</span>
    }

    /**
     * Add an INCRBY operation to the batch.
     *
     * @param key   The key
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addIncrBy(String key, long value) {
<span class="fc" id="L96">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L97">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.INCRBY)</span>
<span class="fc" id="L98">                .setKey(key)</span>
<span class="fc" id="L99">                .setIntValue(value)</span>
<span class="fc" id="L100">                .build();</span>
<span class="fc" id="L101">        operations.add(operation);</span>
<span class="fc" id="L102">        return this;</span>
    }

    /**
     * Add a DECRBY operation to the batch.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The builder instance
     */
    public BatchOperationBuilder addDecrBy(String key, long value) {
<span class="fc" id="L113">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L114">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.DECRBY)</span>
<span class="fc" id="L115">                .setKey(key)</span>
<span class="fc" id="L116">                .setIntValue(value)</span>
<span class="fc" id="L117">                .build();</span>
<span class="fc" id="L118">        operations.add(operation);</span>
<span class="fc" id="L119">        return this;</span>
    }

    /**
     * Add an INCRBYFLOAT operation to the batch.
     *
     * @param key   The key
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addIncrByFloat(String key, double value) {
<span class="fc" id="L130">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L131">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.INCRBYFLOAT)</span>
<span class="fc" id="L132">                .setKey(key)</span>
<span class="fc" id="L133">                .setFloatValue(value)</span>
<span class="fc" id="L134">                .build();</span>
<span class="fc" id="L135">        operations.add(operation);</span>
<span class="fc" id="L136">        return this;</span>
    }

    /**
     * Add an HSET operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return The builder instance
     */
    public BatchOperationBuilder addHSet(String key, String field, String value) {
<span class="fc" id="L148">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L149">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HSET)</span>
<span class="fc" id="L150">                .setKey(key)</span>
<span class="fc" id="L151">                .setField(field)</span>
<span class="fc" id="L152">                .setValue(value)</span>
<span class="fc" id="L153">                .build();</span>
<span class="fc" id="L154">        operations.add(operation);</span>
<span class="fc" id="L155">        return this;</span>
    }

    /**
     * Add an HINCRBY operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addHIncrBy(String key, String field, long value) {
<span class="fc" id="L167">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L168">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HINCRBY)</span>
<span class="fc" id="L169">                .setKey(key)</span>
<span class="fc" id="L170">                .setField(field)</span>
<span class="fc" id="L171">                .setIntValue(value)</span>
<span class="fc" id="L172">                .build();</span>
<span class="fc" id="L173">        operations.add(operation);</span>
<span class="fc" id="L174">        return this;</span>
    }

    /**
     * Add an HDECRBY operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The decrement value
     * @return The builder instance
     */
    public BatchOperationBuilder addHDecrBy(String key, String field, long value) {
<span class="fc" id="L186">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L187">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HDECRBY)</span>
<span class="fc" id="L188">                .setKey(key)</span>
<span class="fc" id="L189">                .setField(field)</span>
<span class="fc" id="L190">                .setIntValue(value)</span>
<span class="fc" id="L191">                .build();</span>
<span class="fc" id="L192">        operations.add(operation);</span>
<span class="fc" id="L193">        return this;</span>
    }

    /**
     * Add an HINCRBYFLOAT operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addHIncrByFloat(String key, String field, double value) {
<span class="fc" id="L205">        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()</span>
<span class="fc" id="L206">                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HINCRBYFLOAT)</span>
<span class="fc" id="L207">                .setKey(key)</span>
<span class="fc" id="L208">                .setField(field)</span>
<span class="fc" id="L209">                .setFloatValue(value)</span>
<span class="fc" id="L210">                .build();</span>
<span class="fc" id="L211">        operations.add(operation);</span>
<span class="fc" id="L212">        return this;</span>
    }

    /**
     * Build the list of batch operations.
     *
     * @return The list of batch operations
     */
    public List&lt;RustyClusterProto.BatchOperation&gt; build() {
<span class="fc" id="L221">        return new ArrayList&lt;&gt;(operations);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>