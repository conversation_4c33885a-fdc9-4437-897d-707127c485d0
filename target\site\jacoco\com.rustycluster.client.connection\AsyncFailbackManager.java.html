<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AsyncFailbackManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.connection</a> &gt; <span class="el_source">AsyncFailbackManager.java</span></div><h1>AsyncFailbackManager.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.connection;

import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import com.rustycluster.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages automatic failback to higher-priority nodes for async operations.
 */
public class AsyncFailbackManager implements AutoCloseable {
<span class="nc" id="L21">    private static final Logger logger = LoggerFactory.getLogger(AsyncFailbackManager.class);</span>

    private final RustyClusterClientConfig config;
    private final AsyncConnectionPool connectionPool;
    private final List&lt;NodeConfig&gt; sortedNodes;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final ScheduledExecutorService scheduler;
<span class="nc" id="L28">    private volatile boolean running = false;</span>

    /**
     * Create a new AsyncFailbackManager.
     *
     * @param config         The client configuration
     * @param connectionPool The async connection pool
     * @param sortedNodes    The list of nodes sorted by priority
     * @param currentNode    The current active node reference
     */
    public AsyncFailbackManager(RustyClusterClientConfig config, 
                               AsyncConnectionPool connectionPool,
                               List&lt;NodeConfig&gt; sortedNodes,
<span class="nc" id="L41">                               AtomicReference&lt;NodeConfig&gt; currentNode) {</span>
<span class="nc" id="L42">        this.config = config;</span>
<span class="nc" id="L43">        this.connectionPool = connectionPool;</span>
<span class="nc" id="L44">        this.sortedNodes = sortedNodes;</span>
<span class="nc" id="L45">        this.currentNode = currentNode;</span>
<span class="nc" id="L46">        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -&gt; {</span>
<span class="nc" id="L47">            Thread t = new Thread(r, &quot;AsyncFailbackManager&quot;);</span>
<span class="nc" id="L48">            t.setDaemon(true);</span>
<span class="nc" id="L49">            return t;</span>
        });
<span class="nc" id="L51">    }</span>

    /**
     * Start the failback manager.
     */
    public void start() {
<span class="nc bnc" id="L57" title="All 2 branches missed.">        if (!config.isEnableFailback()) {</span>
<span class="nc" id="L58">            logger.debug(&quot;Failback is disabled, not starting AsyncFailbackManager&quot;);</span>
<span class="nc" id="L59">            return;</span>
        }

<span class="nc bnc" id="L62" title="All 2 branches missed.">        if (running) {</span>
<span class="nc" id="L63">            logger.warn(&quot;AsyncFailbackManager is already running&quot;);</span>
<span class="nc" id="L64">            return;</span>
        }

<span class="nc" id="L67">        running = true;</span>
<span class="nc" id="L68">        scheduler.scheduleWithFixedDelay(</span>
            this::checkForFailback,
<span class="nc" id="L70">            config.getFailbackCheckIntervalMs(),</span>
<span class="nc" id="L71">            config.getFailbackCheckIntervalMs(),</span>
            TimeUnit.MILLISECONDS
        );
        
<span class="nc" id="L75">        logger.info(&quot;AsyncFailbackManager started with check interval: {}ms&quot;, config.getFailbackCheckIntervalMs());</span>
<span class="nc" id="L76">    }</span>

    /**
     * Stop the failback manager.
     */
    public void stop() {
<span class="nc" id="L82">        running = false;</span>
<span class="nc" id="L83">        scheduler.shutdown();</span>
        try {
<span class="nc bnc" id="L85" title="All 2 branches missed.">            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {</span>
<span class="nc" id="L86">                scheduler.shutdownNow();</span>
            }
<span class="nc" id="L88">        } catch (InterruptedException e) {</span>
<span class="nc" id="L89">            scheduler.shutdownNow();</span>
<span class="nc" id="L90">            Thread.currentThread().interrupt();</span>
<span class="nc" id="L91">        }</span>
<span class="nc" id="L92">        logger.info(&quot;AsyncFailbackManager stopped&quot;);</span>
<span class="nc" id="L93">    }</span>

    /**
     * Check if we can failback to a higher-priority node.
     */
    private void checkForFailback() {
<span class="nc bnc" id="L99" title="All 2 branches missed.">        if (!running) {</span>
<span class="nc" id="L100">            return;</span>
        }

        try {
<span class="nc" id="L104">            NodeConfig current = currentNode.get();</span>
<span class="nc bnc" id="L105" title="All 2 branches missed.">            if (current == null) {</span>
<span class="nc" id="L106">                return;</span>
            }

            // Find the highest priority node that's available
<span class="nc" id="L110">            findBestAvailableNodeAsync()</span>
<span class="nc" id="L111">                .thenAccept(bestAvailableNode -&gt; {</span>
<span class="nc bnc" id="L112" title="All 2 branches missed.">                    if (bestAvailableNode != null &amp;&amp; </span>
<span class="nc bnc" id="L113" title="All 2 branches missed.">                        bestAvailableNode.role().getPriority() &lt; current.role().getPriority()) {</span>
                        
<span class="nc" id="L115">                        logger.info(&quot;Failing back from {} (priority {}) to {} (priority {})&quot;,</span>
<span class="nc" id="L116">                            current, current.role().getPriority(),</span>
<span class="nc" id="L117">                            bestAvailableNode, bestAvailableNode.role().getPriority());</span>
                        
<span class="nc" id="L119">                        currentNode.set(bestAvailableNode);</span>
                    }
<span class="nc" id="L121">                })</span>
<span class="nc" id="L122">                .exceptionally(e -&gt; {</span>
<span class="nc" id="L123">                    logger.warn(&quot;Error during async failback check: {}&quot;, e.getMessage());</span>
<span class="nc" id="L124">                    return null;</span>
                });
<span class="nc" id="L126">        } catch (Exception e) {</span>
<span class="nc" id="L127">            logger.warn(&quot;Error during failback check: {}&quot;, e.getMessage());</span>
<span class="nc" id="L128">        }</span>
<span class="nc" id="L129">    }</span>

    /**
     * Find the best available node (highest priority that's healthy) asynchronously.
     *
     * @return CompletableFuture with the best available node, or null if none are available
     */
    private CompletableFuture&lt;NodeConfig&gt; findBestAvailableNodeAsync() {
<span class="nc" id="L137">        return checkNodesSequentially(0);</span>
    }

    /**
     * Check nodes sequentially starting from the given index.
     *
     * @param nodeIndex The index of the node to check
     * @return CompletableFuture with the first healthy node found, or null
     */
    private CompletableFuture&lt;NodeConfig&gt; checkNodesSequentially(int nodeIndex) {
<span class="nc bnc" id="L147" title="All 2 branches missed.">        if (nodeIndex &gt;= sortedNodes.size()) {</span>
<span class="nc" id="L148">            return CompletableFuture.completedFuture(null);</span>
        }

<span class="nc" id="L151">        NodeConfig node = sortedNodes.get(nodeIndex);</span>
<span class="nc" id="L152">        return isNodeHealthyAsync(node)</span>
<span class="nc" id="L153">            .thenCompose(isHealthy -&gt; {</span>
<span class="nc bnc" id="L154" title="All 2 branches missed.">                if (isHealthy) {</span>
<span class="nc" id="L155">                    return CompletableFuture.completedFuture(node);</span>
                } else {
<span class="nc" id="L157">                    return checkNodesSequentially(nodeIndex + 1);</span>
                }
            });
    }

    /**
     * Check if a node is healthy asynchronously.
     *
     * @param node The node to check
     * @return CompletableFuture with true if the node is healthy, false otherwise
     */
    private CompletableFuture&lt;Boolean&gt; isNodeHealthyAsync(NodeConfig node) {
<span class="nc" id="L169">        return performHealthCheckAsync(node, 0);</span>
    }

    /**
     * Perform health checks recursively with retries.
     *
     * @param node The node to check
     * @param attempt The current attempt number
     * @return CompletableFuture with true if all health checks pass, false otherwise
     */
    private CompletableFuture&lt;Boolean&gt; performHealthCheckAsync(NodeConfig node, int attempt) {
<span class="nc bnc" id="L180" title="All 2 branches missed.">        if (attempt &gt;= config.getFailbackHealthCheckRetries()) {</span>
<span class="nc" id="L181">            return CompletableFuture.completedFuture(true);</span>
        }

<span class="nc" id="L184">        return connectionPool.borrowStubAsync(node)</span>
<span class="nc" id="L185">            .thenCompose(stub -&gt; {</span>
                try {
                    // Apply a short timeout for health checks
<span class="nc" id="L188">                    KeyValueServiceGrpc.KeyValueServiceFutureStub stubWithDeadline = </span>
<span class="nc" id="L189">                        stub.withDeadlineAfter(1000, TimeUnit.MILLISECONDS);</span>
                    
                    // Perform a simple ping operation (get a non-existent key)
<span class="nc" id="L192">                    RustyClusterProto.GetRequest healthCheckRequest = RustyClusterProto.GetRequest.newBuilder()</span>
<span class="nc" id="L193">                        .setKey(&quot;__health_check__&quot;)</span>
<span class="nc" id="L194">                        .build();</span>
                    
<span class="nc" id="L196">                    return toCompletableFuture(stubWithDeadline.get(healthCheckRequest))</span>
<span class="nc" id="L197">                        .thenApply(response -&gt; true)</span>
<span class="nc" id="L198">                        .exceptionally(e -&gt; {</span>
<span class="nc" id="L199">                            logger.debug(&quot;Health check failed for node {} (attempt {}): {}&quot;, </span>
<span class="nc" id="L200">                                node, attempt + 1, e.getMessage());</span>
<span class="nc" id="L201">                            return false;</span>
                        })
<span class="nc" id="L203">                        .whenComplete((result, throwable) -&gt; {</span>
<span class="nc" id="L204">                            connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L205">                        });</span>
<span class="nc" id="L206">                } catch (Exception e) {</span>
<span class="nc" id="L207">                    connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L208">                    return CompletableFuture.completedFuture(false);</span>
                }
            })
<span class="nc" id="L211">            .thenCompose(success -&gt; {</span>
<span class="nc bnc" id="L212" title="All 4 branches missed.">                if (success &amp;&amp; attempt &lt; config.getFailbackHealthCheckRetries() - 1) {</span>
                    // Add a small delay between checks
<span class="nc" id="L214">                    CompletableFuture&lt;Void&gt; delay = new CompletableFuture&lt;&gt;();</span>
<span class="nc" id="L215">                    CompletableFuture.delayedExecutor(100, TimeUnit.MILLISECONDS)</span>
<span class="nc" id="L216">                        .execute(() -&gt; delay.complete(null));</span>
<span class="nc" id="L217">                    return delay.thenCompose(v -&gt; performHealthCheckAsync(node, attempt + 1));</span>
                } else {
<span class="nc" id="L219">                    return CompletableFuture.completedFuture(success);</span>
                }
            })
<span class="nc" id="L222">            .exceptionally(e -&gt; {</span>
<span class="nc" id="L223">                logger.debug(&quot;Health check failed for node {} (attempt {}): {}&quot;, </span>
<span class="nc" id="L224">                    node, attempt + 1, e.getMessage());</span>
<span class="nc" id="L225">                return false;</span>
            });
    }

    /**
     * Convert a ListenableFuture to CompletableFuture.
     */
    private &lt;T&gt; CompletableFuture&lt;T&gt; toCompletableFuture(com.google.common.util.concurrent.ListenableFuture&lt;T&gt; listenableFuture) {
<span class="nc" id="L233">        CompletableFuture&lt;T&gt; completableFuture = new CompletableFuture&lt;&gt;();</span>
<span class="nc" id="L234">        com.google.common.util.concurrent.Futures.addCallback(</span>
            listenableFuture,
<span class="nc" id="L236">            new com.google.common.util.concurrent.FutureCallback&lt;T&gt;() {</span>
                @Override
                public void onSuccess(T result) {
<span class="nc" id="L239">                    completableFuture.complete(result);</span>
<span class="nc" id="L240">                }</span>

                @Override
                public void onFailure(Throwable t) {
<span class="nc" id="L244">                    completableFuture.completeExceptionally(t);</span>
<span class="nc" id="L245">                }</span>
            },
            Runnable::run
        );
<span class="nc" id="L249">        return completableFuture;</span>
    }

    @Override
    public void close() {
<span class="nc" id="L254">        stop();</span>
<span class="nc" id="L255">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>