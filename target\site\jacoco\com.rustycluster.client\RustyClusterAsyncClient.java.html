<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterAsyncClient.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client</a> &gt; <span class="el_source">RustyClusterAsyncClient.java</span></div><h1>RustyClusterAsyncClient.java</h1><pre class="source lang-java linenums">package com.rustycluster.client;

import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.connection.AsyncConnectionManager;
import com.rustycluster.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Asynchronous client for interacting with RustyCluster.
 * This client provides non-blocking operations for high-throughput scenarios.
 */
public class RustyClusterAsyncClient implements AutoCloseable {
<span class="nc" id="L19">    private static final Logger logger = LoggerFactory.getLogger(RustyClusterAsyncClient.class);</span>

    private final RustyClusterClientConfig config;
    private final AsyncConnectionManager connectionManager;
    private final AuthenticationManager authenticationManager;

    /**
     * Create a new RustyClusterAsyncClient with the provided configuration.
     *
     * @param config The client configuration
     */
<span class="nc" id="L30">    public RustyClusterAsyncClient(RustyClusterClientConfig config) {</span>
<span class="nc" id="L31">        this.config = config;</span>
<span class="nc" id="L32">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="nc" id="L33">        this.connectionManager = new AsyncConnectionManager(config, authenticationManager);</span>
<span class="nc" id="L34">        logger.info(&quot;RustyClusterAsyncClient initialized&quot;);</span>
<span class="nc" id="L35">    }</span>

    /**
     * Set a key-value pair asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setAsync(String key, String value, boolean skipReplication) {
<span class="nc" id="L46">        logger.debug(&quot;Setting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L47">        RustyClusterProto.SetRequest request = RustyClusterProto.SetRequest.newBuilder()</span>
<span class="nc" id="L48">                .setKey(key)</span>
<span class="nc" id="L49">                .setValue(value)</span>
<span class="nc" id="L50">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L51">                .build();</span>

<span class="nc" id="L53">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L54">                stub.set(request))</span>
<span class="nc" id="L55">                .thenApply(RustyClusterProto.SetResponse::getSuccess);</span>
    }

    /**
     * Set a key-value pair asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setAsync(String key, String value) {
<span class="nc" id="L66">        return setAsync(key, value, false);</span>
    }

    /**
     * Get a value by key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with the value, or null if not found
     */
    public CompletableFuture&lt;String&gt; getAsync(String key) {
<span class="nc" id="L76">        logger.debug(&quot;Getting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L77">        RustyClusterProto.GetRequest request = RustyClusterProto.GetRequest.newBuilder()</span>
<span class="nc" id="L78">                .setKey(key)</span>
<span class="nc" id="L79">                .build();</span>

<span class="nc" id="L81">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L82">                stub.get(request))</span>
<span class="nc bnc" id="L83" title="All 2 branches missed.">                .thenApply(response -&gt; response.getFound() ? response.getValue() : null);</span>
    }

    /**
     * Delete a key asynchronously.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; deleteAsync(String key, boolean skipReplication) {
<span class="nc" id="L94">        logger.debug(&quot;Deleting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L95">        RustyClusterProto.DeleteRequest request = RustyClusterProto.DeleteRequest.newBuilder()</span>
<span class="nc" id="L96">                .setKey(key)</span>
<span class="nc" id="L97">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L98">                .build();</span>

<span class="nc" id="L100">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L101">                stub.delete(request))</span>
<span class="nc" id="L102">                .thenApply(RustyClusterProto.DeleteResponse::getSuccess);</span>
    }

    /**
     * Delete a key asynchronously with default replication.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; deleteAsync(String key) {
<span class="nc" id="L112">        return deleteAsync(key, false);</span>
    }

    /**
     * Execute a batch of operations asynchronously.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture&lt;List&lt;Boolean&gt;&gt; batchWriteAsync(List&lt;RustyClusterProto.BatchOperation&gt; operations, boolean skipReplication) {
<span class="nc" id="L123">        logger.debug(&quot;Executing batch write asynchronously with {} operations&quot;, operations.size());</span>
<span class="nc" id="L124">        RustyClusterProto.BatchWriteRequest request = RustyClusterProto.BatchWriteRequest.newBuilder()</span>
<span class="nc" id="L125">                .addAllOperations(operations)</span>
<span class="nc" id="L126">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L127">                .build();</span>

<span class="nc" id="L129">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L130">                stub.batchWrite(request))</span>
<span class="nc" id="L131">                .thenApply(RustyClusterProto.BatchWriteResponse::getOperationResultsList);</span>
    }

    /**
     * Execute a batch of operations asynchronously with default replication.
     *
     * @param operations The list of operations to execute
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture&lt;List&lt;Boolean&gt;&gt; batchWriteAsync(List&lt;RustyClusterProto.BatchOperation&gt; operations) {
<span class="nc" id="L141">        return batchWriteAsync(operations, false);</span>
    }

    /**
     * Increment a numeric value asynchronously.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture&lt;Long&gt; incrByAsync(String key, long value, boolean skipReplication) {
<span class="nc" id="L153">        logger.debug(&quot;Incrementing key asynchronously: {} by {}&quot;, key, value);</span>
<span class="nc" id="L154">        RustyClusterProto.IncrByRequest request = RustyClusterProto.IncrByRequest.newBuilder()</span>
<span class="nc" id="L155">                .setKey(key)</span>
<span class="nc" id="L156">                .setValue(value)</span>
<span class="nc" id="L157">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L158">                .build();</span>

<span class="nc" id="L160">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L161">                stub.incrBy(request))</span>
<span class="nc" id="L162">                .thenApply(RustyClusterProto.IncrByResponse::getNewValue);</span>
    }

    /**
     * Increment a numeric value asynchronously with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture&lt;Long&gt; incrByAsync(String key, long value) {
<span class="nc" id="L173">        return incrByAsync(key, value, false);</span>
    }

    /**
     * Get all fields from a hash asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with a map of field names to values
     */
    public CompletableFuture&lt;Map&lt;String, String&gt;&gt; hGetAllAsync(String key) {
<span class="nc" id="L183">        logger.debug(&quot;Getting all hash fields asynchronously for key: {}&quot;, key);</span>
<span class="nc" id="L184">        RustyClusterProto.HGetAllRequest request = RustyClusterProto.HGetAllRequest.newBuilder()</span>
<span class="nc" id="L185">                .setKey(key)</span>
<span class="nc" id="L186">                .build();</span>

<span class="nc" id="L188">        return connectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L189">                stub.hGetAll(request))</span>
<span class="nc" id="L190">                .thenApply(RustyClusterProto.HGetAllResponse::getFieldsMap);</span>
    }

    /**
     * Authenticate with the RustyCluster server asynchronously.
     *
     * @return CompletableFuture that completes with true if authentication was successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; authenticateAsync() {
<span class="nc" id="L199">        logger.debug(&quot;Attempting to authenticate asynchronously with RustyCluster server&quot;);</span>

<span class="nc bnc" id="L201" title="All 2 branches missed.">        if (!config.hasAuthentication()) {</span>
<span class="nc" id="L202">            logger.debug(&quot;No authentication credentials configured&quot;);</span>
<span class="nc" id="L203">            return CompletableFuture.completedFuture(true);</span>
        }

        // For async authentication, we need to use a different approach
        // since authenticate method expects a blocking stub
<span class="nc" id="L208">        return CompletableFuture.supplyAsync(() -&gt; {</span>
            try {
                // This is a simplified approach - in a real implementation,
                // you'd want to create an async version of authenticate
<span class="nc" id="L212">                return authenticationManager.isAuthenticated();</span>
<span class="nc" id="L213">            } catch (Exception e) {</span>
<span class="nc" id="L214">                logger.error(&quot;Authentication check failed&quot;, e);</span>
<span class="nc" id="L215">                return false;</span>
            }
        });
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
<span class="nc" id="L226">        return authenticationManager.isAuthenticated();</span>
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
<span class="nc" id="L235">        return authenticationManager.getSessionToken();</span>
    }

    /**
     * Close the client and release all resources.
     */
    @Override
    public void close() {
<span class="nc" id="L243">        authenticationManager.clearAuthentication();</span>
<span class="nc" id="L244">        connectionManager.close();</span>
<span class="nc" id="L245">        logger.info(&quot;RustyClusterAsyncClient closed&quot;);</span>
<span class="nc" id="L246">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>