package com.rustycluster.client.connection;

import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.netty.shaded.io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.handler.ssl.SslContext;

import java.io.File;
import java.util.concurrent.TimeUnit;

/**
 * Factory for creating gRPC channels to RustyCluster nodes.
 */
public class GrpcChannelFactory {
    private final RustyClusterClientConfig config;

    /**
     * Create a new GrpcChannelFactory.
     *
     * @param config The client configuration
     */
    public GrpcChannelFactory(RustyClusterClientConfig config) {
        this.config = config;
    }

    /**
     * Create a new gRPC channel for the given node.
     *
     * @param nodeConfig The node configuration
     * @return A new ManagedChannel instance
     */
    public ManagedChannel createChannel(NodeConfig nodeConfig) {
        if (config.isUseSecureConnection()) {
            return createSecureChannel(nodeConfig);
        } else {
            return createInsecureChannel(nodeConfig);
        }
    }

    private ManagedChannel createInsecureChannel(NodeConfig nodeConfig) {
        return ManagedChannelBuilder.forAddress(nodeConfig.host(), nodeConfig.port())
                .usePlaintext()
                // High-throughput keep-alive settings
                .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive
                .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection
                .keepAliveWithoutCalls(true)
                // Performance optimizations
                .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches
                .maxInboundMetadataSize(8 * 1024) // 8KB metadata
                // Connection management
                .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections
                // Retry configuration
                .enableRetry()
                .maxRetryAttempts(3)
                .build();
    }

    private ManagedChannel createSecureChannel(NodeConfig nodeConfig) {
        try {
            SslContext sslContext = GrpcSslContexts.forClient()
                    .trustManager(new File(config.getTlsCertPath()))
                    .build();

            return NettyChannelBuilder.forAddress(nodeConfig.host(), nodeConfig.port())
                    .sslContext(sslContext)
                    // High-throughput keep-alive settings
                    .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive
                    .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection
                    .keepAliveWithoutCalls(true)
                    // Performance optimizations
                    .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches
                    .maxInboundMetadataSize(8 * 1024) // 8KB metadata
                    // Connection management
                    .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections
                    // Retry configuration
                    .enableRetry()
                    .maxRetryAttempts(3)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create secure channel", e);
        }
    }
}
