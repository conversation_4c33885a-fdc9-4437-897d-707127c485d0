<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NoAvailableNodesException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.exception</a> &gt; <span class="el_source">NoAvailableNodesException.java</span></div><h1>NoAvailableNodesException.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.exception;

/**
 * Exception thrown when no available nodes can be found.
 */
public class NoAvailableNodesException extends RuntimeException {
    /**
     * Create a new NoAvailableNodesException with a message.
     *
     * @param message The error message
     */
    public NoAvailableNodesException(String message) {
<span class="nc" id="L13">        super(message);</span>
<span class="nc" id="L14">    }</span>

    /**
     * Create a new NoAvailableNodesException with a message and cause.
     *
     * @param message The error message
     * @param cause   The cause of the exception
     */
    public NoAvailableNodesException(String message, Throwable cause) {
<span class="fc" id="L23">        super(message, cause);</span>
<span class="fc" id="L24">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>