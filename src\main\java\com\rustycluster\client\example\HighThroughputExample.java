package com.rustycluster.client.example;

import com.rustycluster.client.BatchOperationBuilder;
import com.rustycluster.client.RustyClusterAsyncClient;
import com.rustycluster.client.RustyClusterClient;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Example demonstrating high-throughput usage of RustyCluster Java client.
 * This example shows various optimization techniques for maximum performance.
 */
public class HighThroughputExample {
    private static final Logger logger = LoggerFactory.getLogger(HighThroughputExample.class);

    public static void main(String[] args) {
        // High-throughput configuration
         RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051", "localhost:50052", "localhost:50053")
                .maxConnectionsPerNode(1000)
                .connectionTimeout(2, TimeUnit.SECONDS)
                .readTimeout(2, TimeUnit.SECONDS)
                .writeTimeout(3, TimeUnit.SECONDS)
                .maxRetries(3)
                .retryDelay(500, TimeUnit.MILLISECONDS)
                .authentication("admin", "npci")  // Add authentication credentials
                .build();

        logger.info("Starting high-throughput performance demonstration");

        // Demonstrate different performance optimization techniques
        //demonstrateSyncBatchOperations(config);
        //demonstrateAsyncOperations(config);
        demonstrateConcurrentOperations(config);

        logger.info("High-throughput demonstration completed");
    }

    /**
     * Demonstrate high-throughput synchronous batch operations.
     */
    private static void demonstrateSyncBatchOperations(RustyClusterClientConfig config) {
        logger.info("=== Synchronous Batch Operations Demo ===");

        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Create large batch operations
            int batchSize = 1000;
            int numBatches = 10;
            long startTime = System.currentTimeMillis();
            int totalOperations = 0;

            for (int batch = 0; batch < numBatches; batch++) {
                BatchOperationBuilder builder = new BatchOperationBuilder();

                // Build batch with mixed operations
                for (int i = 0; i < batchSize; i++) {
                    String key = "batch_" + batch + "_key_" + i;
                    String value = "value_" + i;

                    if (i % 3 == 0) {
                        builder.addSet(key, value);
                    } else if (i % 3 == 1) {
                        builder.addIncrBy(key, i);
                    } else {
                        builder.addHSet("hash_" + batch, "field_" + i, value);
                    }
                }

                List<RustyClusterProto.BatchOperation> operations = builder.build();
                List<Boolean> results = client.batchWrite(operations);

                long successCount = results.stream().mapToLong(success -> success ? 1 : 0).sum();
                totalOperations += batchSize;
                logger.info("Batch {} completed: {}/{} operations successful",
                    batch + 1, successCount, batchSize);
            }

            // Calculate and log performance statistics
            long endTime = System.currentTimeMillis();
            double durationSeconds = (endTime - startTime) / 1000.0;
            double throughput = totalOperations / durationSeconds;
            logger.info("Sync batch demo completed: {} operations in {:.2f}s, {:.2f} ops/sec throughput",
                totalOperations, durationSeconds, throughput);

        } catch (Exception e) {
            logger.error("Error in sync batch operations demo", e);
        }
    }

    /**
     * Demonstrate high-throughput asynchronous operations.
     */
    private static void demonstrateAsyncOperations(RustyClusterClientConfig config) {
        logger.info("=== Asynchronous Operations Demo ===");

        try (RustyClusterAsyncClient asyncClient = new RustyClusterAsyncClient(config)) {
            int numOperations = 10000;
            List<CompletableFuture<Boolean>> futures = new ArrayList<>();
            long startTime = System.currentTimeMillis();

            // Launch many async operations concurrently
            for (int i = 0; i < numOperations; i++) {
                String key = "async_key_" + i;
                String value = "async_value_" + i;

                CompletableFuture<Boolean> future = asyncClient.setAsync(key, value);
                futures.add(future);
            }

            // Wait for all operations to complete
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));

            allFutures.join();

            // Count successful operations
            long successCount = futures.stream()
                .mapToLong(future -> {
                    try {
                        return future.get() ? 1 : 0;
                    } catch (Exception e) {
                        return 0;
                    }
                })
                .sum();

            // Calculate and log performance statistics
            long endTime = System.currentTimeMillis();
            double durationSeconds = (endTime - startTime) / 1000.0;
            double throughput = numOperations / durationSeconds;

            logger.info("Async demo completed: {}/{} operations successful in {:.2f}s, {:.2f} ops/sec throughput",
                successCount, numOperations, durationSeconds, throughput);

        } catch (Exception e) {
            logger.error("Error in async operations demo", e);
        }
    }

    /**
     * Demonstrate concurrent operations using multiple threads.
     */
    private static void demonstrateConcurrentOperations(RustyClusterClientConfig config) {
        logger.info("=== Concurrent Operations Demo ===");

        int numThreads = 10;
        int operationsPerThread = 1000;
        ExecutorService executor = Executors.newFixedThreadPool(numThreads);

        try (RustyClusterClient client = new RustyClusterClient(config)) {
            boolean authResult = client.authenticate();
            if (!authResult) {
                logger.error("Authentication failed, exiting concurrent demo");
                return;
            }

            List<CompletableFuture<Void>> threadFutures = new ArrayList<>();
            long startTime = System.currentTimeMillis();
            int totalOperations = numThreads * operationsPerThread;

            // Launch concurrent worker threads
            for (int thread = 0; thread < numThreads; thread++) {
                final int threadId = thread;

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        for (int op = 0; op < operationsPerThread; op++) {
                            String key = "thread_" + threadId + "_key_" + op;
                            String value = "thread_" + threadId + "_value_" + op;

                            // Mix of different operations
                            if (op % 4 == 0) {
                                client.set(key, value);
                            } else if (op % 4 == 1) {
                                client.get(key);
                            } else if (op % 4 == 2) {
                                client.incrBy(key, op);
                            } else {
                                client.hSet("hash_" + threadId, "field_" + op, value);
                            }
                        }
                        logger.info("Thread {} completed {} operations", threadId, operationsPerThread);
                    } catch (Exception e) {
                        logger.error("Error in thread " + threadId, e);
                    }
                }, executor);

                threadFutures.add(future);
            }

            // Wait for all threads to complete
            CompletableFuture.allOf(threadFutures.toArray(new CompletableFuture[0])).join();

            // Calculate and log final performance statistics
            long endTime = System.currentTimeMillis();
            double durationSeconds = (endTime - startTime) / 1000.0;
            double throughput = totalOperations / durationSeconds;
            logger.info("Concurrent demo completed: {} operations in {:.2f}s, {:.2f} ops/sec throughput",
                totalOperations, durationSeconds, throughput);

        } catch (Exception e) {
            logger.error("Error in concurrent operations demo", e);
        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
