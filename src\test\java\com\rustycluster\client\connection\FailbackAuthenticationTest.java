package com.rustycluster.client.connection;

import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.NodeRole;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import com.rustycluster.grpc.RustyClusterProto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FailbackAuthenticationTest {

    @Mock
    private ConnectionPool connectionPool;

    @Mock
    private AuthenticationManager authenticationManager;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub mockStub;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub mockStubWithDeadline;

    private RustyClusterClientConfig config;
    private List<NodeConfig> sortedNodes;
    private AtomicReference<NodeConfig> currentNode;

    @BeforeEach
    void setUp() {
        // Create config with authentication enabled
        config = RustyClusterClientConfig.builder()
                .addNodes("primary:50051", "secondary:50052")
                .authentication("testuser", "testpass")
                .enableFailback(true)
                .failbackCheckIntervalMs(1000)
                .build();

        // Create sorted nodes (primary has higher priority)
        NodeConfig primaryNode = new NodeConfig("primary:50051", NodeRole.PRIMARY);
        NodeConfig secondaryNode = new NodeConfig("secondary:50052", NodeRole.SECONDARY);
        sortedNodes = List.of(primaryNode, secondaryNode);

        // Start with secondary node (simulating after failover)
        currentNode = new AtomicReference<>(secondaryNode);

        // Mock connection pool to return authentication manager
        when(connectionPool.getAuthenticationManager()).thenReturn(authenticationManager);
    }

    @Test
    @DisplayName("Should clear authentication when failing back to primary node")
    void shouldClearAuthenticationWhenFailingBackToPrimaryNode() throws Exception {
        NodeConfig primaryNode = sortedNodes.get(0);
        NodeConfig secondaryNode = sortedNodes.get(1);

        // Mock primary node as healthy
        when(connectionPool.borrowStub(primaryNode)).thenReturn(mockStub);
        when(mockStub.withDeadlineAfter(eq(1000L), eq(TimeUnit.MILLISECONDS)))
                .thenReturn(mockStubWithDeadline);
        when(mockStubWithDeadline.get(any(RustyClusterProto.GetRequest.class)))
                .thenReturn(RustyClusterProto.GetResponse.newBuilder().build());

        try (FailbackManager failbackManager = new FailbackManager(
                config, connectionPool, sortedNodes, currentNode)) {

            // Verify we start with secondary node
            assert currentNode.get().equals(secondaryNode);

            // Manually trigger failback check (simulating the scheduled check)
            failbackManager.start();
            
            // Wait a bit for the failback check to run
            Thread.sleep(1500);

            // Verify that we switched back to primary node
            assert currentNode.get().equals(primaryNode);

            // Verify that authentication was cleared when switching back
            verify(authenticationManager, atLeastOnce()).clearAuthentication();
        }
    }

    @Test
    @DisplayName("Should not clear authentication when no failback occurs")
    void shouldNotClearAuthenticationWhenNoFailbackOccurs() throws Exception {
        NodeConfig primaryNode = sortedNodes.get(0);

        // Mock primary node as unhealthy (connection fails)
        when(connectionPool.borrowStub(primaryNode))
                .thenThrow(new RuntimeException("Connection failed"));

        try (FailbackManager failbackManager = new FailbackManager(
                config, connectionPool, sortedNodes, currentNode)) {

            failbackManager.start();
            
            // Wait a bit for the failback check to run
            Thread.sleep(1500);

            // Verify that authentication was NOT cleared since no failback occurred
            verify(authenticationManager, never()).clearAuthentication();
        }
    }

    @Test
    @DisplayName("Should not clear authentication when authentication is disabled")
    void shouldNotClearAuthenticationWhenAuthenticationDisabled() throws Exception {
        // Create config without authentication
        RustyClusterClientConfig configNoAuth = RustyClusterClientConfig.builder()
                .addNodes("primary:50051", "secondary:50052")
                .enableFailback(true)
                .failbackCheckIntervalMs(1000)
                .build();

        NodeConfig primaryNode = sortedNodes.get(0);

        // Mock primary node as healthy
        when(connectionPool.borrowStub(primaryNode)).thenReturn(mockStub);
        when(mockStub.withDeadlineAfter(eq(1000L), eq(TimeUnit.MILLISECONDS)))
                .thenReturn(mockStubWithDeadline);
        when(mockStubWithDeadline.get(any(RustyClusterProto.GetRequest.class)))
                .thenReturn(RustyClusterProto.GetResponse.newBuilder().build());

        try (FailbackManager failbackManager = new FailbackManager(
                configNoAuth, connectionPool, sortedNodes, currentNode)) {

            failbackManager.start();
            
            // Wait a bit for the failback check to run
            Thread.sleep(1500);

            // Verify that authentication was NOT cleared since authentication is disabled
            verify(authenticationManager, never()).clearAuthentication();
        }
    }
}
