package com.rustycluster.client.connection;

import com.google.common.util.concurrent.ListenableFuture;
import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.exception.NoAvailableNodesException;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages asynchronous connections to RustyCluster nodes, handling prioritization and failover.
 */
public class AsyncConnectionManager implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(AsyncConnectionManager.class);

    private final RustyClusterClientConfig config;
    private final AsyncConnectionPool connectionPool;
    private final AtomicReference<NodeConfig> currentNode;
    private final List<NodeConfig> sortedNodes;
    private final Executor executor;
    private final AsyncFailbackManager failbackManager;

    /**
     * Create a new AsyncConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public AsyncConnectionManager(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
        this(config, new AsyncConnectionPool(config, authenticationManager));
    }

    /**
     * Create a new AsyncConnectionManager with a custom connection pool (for testing).
     *
     * @param config The client configuration
     * @param connectionPool The connection pool to use
     */
    AsyncConnectionManager(RustyClusterClientConfig config, AsyncConnectionPool connectionPool) {
        this.config = config;
        this.connectionPool = connectionPool;
        this.executor = ForkJoinPool.commonPool(); // Use common pool for async operations

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
        this.sortedNodes = config.getNodes().stream()
                .sorted(Comparator.comparingInt(node -> node.role().getPriority()))
                .toList();

        // Set the initial node to the highest priority node
        this.currentNode = new AtomicReference<>(sortedNodes.get(0));

        // For async operations, we'll use a synchronous FailbackManager with the async pool
        // The health checks will be performed synchronously but the failback manager itself
        // runs in a separate thread, so it won't block async operations
        this.failbackManager = new AsyncFailbackManager(config, connectionPool, sortedNodes, currentNode);
        this.failbackManager.start();

        logger.info("AsyncConnectionManager initialized with {} nodes", sortedNodes.size());
    }

    /**
     * Execute an operation asynchronously with automatic failover.
     *
     * @param operation The operation to execute
     * @param <T>       The return type of the operation
     * @return CompletableFuture that completes with the result of the operation
     */
    public <T> CompletableFuture<T> executeWithFailoverAsync(AsyncClientOperation<T> operation) {
        return executeWithFailoverAsync(operation, OperationType.READ, 0);
    }

    /**
     * Execute an operation asynchronously with automatic failover and specific timeout based on operation type.
     *
     * @param operation     The operation to execute
     * @param operationType The type of operation (READ, WRITE, AUTH) to determine appropriate timeout
     * @param <T>           The return type of the operation
     * @return CompletableFuture that completes with the result of the operation
     */
    public <T> CompletableFuture<T> executeWithFailoverAsync(AsyncClientOperation<T> operation, OperationType operationType) {
        return executeWithFailoverAsync(operation, operationType, 0);
    }

    private <T> CompletableFuture<T> executeWithFailoverAsync(AsyncClientOperation<T> operation, OperationType operationType, int retryCount) {
        if (retryCount > config.getMaxRetries()) {
            return CompletableFuture.failedFuture(
                new NoAvailableNodesException("Operation failed after " + retryCount + " retries"));
        }

        // Determine timeout based on operation type
        long timeoutMs = switch (operationType) {
            case READ -> config.getReadTimeoutMs();
            case WRITE -> config.getWriteTimeoutMs();
            case AUTH -> config.getConnectionTimeoutMs();
        };

        NodeConfig node = currentNode.get();

        return connectionPool.borrowStubAsync(node)
            .thenCompose(stub -> {
                try {
                    // Apply deadline per operation to avoid expired deadline issues
                    KeyValueServiceGrpc.KeyValueServiceFutureStub stubWithDeadline =
                        stub.withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);
                    ListenableFuture<T> listenableFuture = operation.execute(stubWithDeadline);
                    return toCompletableFuture(listenableFuture)
                        .whenComplete((result, throwable) -> {
                            connectionPool.returnStub(node, stub);
                        });
                } catch (Exception e) {
                    connectionPool.returnStub(node, stub);
                    return CompletableFuture.failedFuture(e);
                }
            })
            .exceptionally(throwable -> {
                logger.warn("Operation failed on node {}: {}", node, throwable.getMessage());

                // Try to find the next available node
                var nextNode = findNextAvailableNode(node);
                if (nextNode != null) {
                    currentNode.set(nextNode);
                    logger.info("Switched to node: {}", nextNode);

                    // Clear authentication state when switching nodes
                    // This will force re-authentication on the next operation
                    if (config.hasAuthentication()) {
                        connectionPool.getAuthenticationManager().clearAuthentication();
                        logger.debug("Cleared authentication state for node switch to: {}", nextNode);
                    }
                }

                throw new RuntimeException(throwable);
            })
            .handle((result, throwable) -> {
                if (throwable != null) {
                    // Retry with delay
                    CompletableFuture<Void> delay = new CompletableFuture<>();
                    CompletableFuture.delayedExecutor(config.getRetryDelayMs(),
                            java.util.concurrent.TimeUnit.MILLISECONDS, executor)
                        .execute(() -> delay.complete(null));
                    return delay.thenCompose(v -> executeWithFailoverAsync(operation, operationType, retryCount + 1));
                }
                return CompletableFuture.completedFuture(result);
            })
            .thenCompose(future -> future instanceof CompletableFuture ?
                (CompletableFuture<T>) future : CompletableFuture.completedFuture((T) future));
    }

    /**
     * Convert ListenableFuture to CompletableFuture.
     */
    private <T> CompletableFuture<T> toCompletableFuture(ListenableFuture<T> listenableFuture) {
        CompletableFuture<T> completableFuture = new CompletableFuture<>();

        listenableFuture.addListener(() -> {
            try {
                completableFuture.complete(listenableFuture.get());
            } catch (Exception e) {
                completableFuture.completeExceptionally(e);
            }
        }, executor);

        return completableFuture;
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none are available
     */
    private NodeConfig findNextAvailableNode(NodeConfig failedNode) {
        // First try to find a node with the same priority
        var samePriorityNode = sortedNodes.stream()
                .filter(node -> node.role() == failedNode.role() && !node.equals(failedNode))
                .filter(this::isNodeAvailable)
                .findFirst();

        if (samePriorityNode.isPresent()) {
            return samePriorityNode.get();
        }

        // Then try to find a node with lower priority
        var lowerPriorityNode = sortedNodes.stream()
                .filter(node -> node.role().getPriority() > failedNode.role().getPriority())
                .filter(this::isNodeAvailable)
                .findFirst();

        if (lowerPriorityNode.isPresent()) {
            return lowerPriorityNode.get();
        }

        // Finally, try any node except the failed one
        return sortedNodes.stream()
                .filter(node -> !node.equals(failedNode))
                .filter(this::isNodeAvailable)
                .findFirst()
                .orElse(null);
    }

    /**
     * Check if a node is available.
     *
     * @param node The node to check
     * @return True if the node is available, false otherwise
     */
    private boolean isNodeAvailable(NodeConfig node) {
        try {
            // For async operations, we'll do a simple check
            // In a real implementation, you might want to perform an async health check
            return connectionPool.borrowStubAsync(node)
                .thenApply(stub -> {
                    connectionPool.returnStub(node, stub);
                    return true;
                })
                .exceptionally(e -> {
                    logger.warn("Node {} is not available: {}", node, e.getMessage());
                    return false;
                })
                .join(); // This is not ideal for async, but needed for this interface
        } catch (Exception e) {
            logger.warn("Node {} is not available: {}", node, e.getMessage());
            return false;
        }
    }



    /**
     * Close the connection manager and release all resources.
     */
    @Override
    public void close() {
        failbackManager.close();
        connectionPool.close();
        logger.info("AsyncConnectionManager closed");
    }

    /**
     * Functional interface for async client operations.
     *
     * @param <T> The return type of the operation
     */
    @FunctionalInterface
    public interface AsyncClientOperation<T> {
        /**
         * Execute an operation using the provided client stub.
         *
         * @param stub The client stub
         * @return ListenableFuture with the result of the operation
         * @throws Exception If the operation fails
         */
        ListenableFuture<T> execute(KeyValueServiceGrpc.KeyValueServiceFutureStub stub) throws Exception;
    }
}
