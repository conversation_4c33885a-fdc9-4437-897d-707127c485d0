<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyLoadExample.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.example</a> &gt; <span class="el_source">RustyLoadExample.java</span></div><h1>RustyLoadExample.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.example;

import com.rustycluster.client.RustyClusterClient;
import com.rustycluster.client.config.RustyClusterClientConfig;

import java.util.Random;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * A simple load test for RustyClusterClient.
 */
<span class="nc" id="L13">public class RustyLoadExample {</span>

    private static final int NUM_THREADS = 10;
    private static final int TEST_DURATION_SECONDS = 30;

    public static void main(String[] args) throws InterruptedException {
<span class="nc" id="L19">        RustyClusterClientConfig config = RustyClusterClientConfig.builder()</span>
<span class="nc" id="L20">                .addNodes(&quot;localhost:50051&quot;, &quot;localhost:50052&quot;, &quot;localhost:50053&quot;)</span>
<span class="nc" id="L21">                .maxConnectionsPerNode(128)</span>
<span class="nc" id="L22">                .connectionTimeout(5, TimeUnit.SECONDS)</span>
<span class="nc" id="L23">                .readTimeout(3, TimeUnit.SECONDS)</span>
<span class="nc" id="L24">                .writeTimeout(3, TimeUnit.SECONDS)</span>
<span class="nc" id="L25">                .maxRetries(3)</span>
<span class="nc" id="L26">                .retryDelay(500, TimeUnit.MILLISECONDS)</span>
<span class="nc" id="L27">                .authentication(&quot;admin&quot;, &quot;npci&quot;)</span>
<span class="nc" id="L28">                .build();</span>

        // Create client
<span class="nc" id="L31">        try (RustyClusterClient client = new RustyClusterClient(config)) {</span>
<span class="nc bnc" id="L32" title="All 2 branches missed.">            if (!client.authenticate()) {</span>
<span class="nc" id="L33">                System.err.println(&quot;Authentication failed. Exiting...&quot;);</span>
<span class="nc" id="L34">                return;</span>
            }

<span class="nc" id="L37">            System.out.println(&quot;Authentication successful. Starting load test...&quot;);</span>

<span class="nc" id="L39">            ExecutorService executor = Executors.newFixedThreadPool(NUM_THREADS);</span>
<span class="nc" id="L40">            AtomicLong totalOps = new AtomicLong(0);</span>
<span class="nc" id="L41">            AtomicLong totalLatencyNs = new AtomicLong(0);</span>
<span class="nc" id="L42">            Random random = new Random();</span>

<span class="nc" id="L44">            long endTime = System.nanoTime() + TimeUnit.SECONDS.toNanos(TEST_DURATION_SECONDS);</span>

<span class="nc" id="L46">            Runnable worker = () -&gt; {</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">                while (System.nanoTime() &lt; endTime) {</span>
<span class="nc" id="L48">                    String key = &quot;key&quot; + random.nextInt(1000);</span>
<span class="nc" id="L49">                    String value = &quot;val&quot; + random.nextInt(1000);</span>

<span class="nc" id="L51">                    long start = System.nanoTime();</span>

                    // Randomly choose an operation
<span class="nc" id="L54">                    int op = random.nextInt(4);</span>
                    try {
<span class="nc bnc" id="L56" title="All 5 branches missed.">                        switch (op) {</span>
<span class="nc" id="L57">                            case 0 -&gt; client.set(key, value);</span>
<span class="nc" id="L58">                            case 1 -&gt; client.get(key);</span>
<span class="nc" id="L59">                            case 2 -&gt; client.incrBy(&quot;counter&quot;, 1);</span>
<span class="nc" id="L60">                            case 3 -&gt; client.hSet(&quot;hash:loadtest&quot;, &quot;field&quot; + random.nextInt(10), value);</span>
                        }
<span class="nc" id="L62">                        long latency = System.nanoTime() - start;</span>
<span class="nc" id="L63">                        totalOps.incrementAndGet();</span>
<span class="nc" id="L64">                        totalLatencyNs.addAndGet(latency);</span>
<span class="nc" id="L65">                    } catch (Exception e) {</span>
<span class="nc" id="L66">                        e.printStackTrace();</span>
<span class="nc" id="L67">                    }</span>
<span class="nc" id="L68">                }</span>
<span class="nc" id="L69">            };</span>

<span class="nc bnc" id="L71" title="All 2 branches missed.">            for (int i = 0; i &lt; NUM_THREADS; i++) {</span>
<span class="nc" id="L72">                executor.submit(worker);</span>
            }

<span class="nc" id="L75">            executor.shutdown();</span>
<span class="nc" id="L76">            executor.awaitTermination(TEST_DURATION_SECONDS + 5, TimeUnit.SECONDS);</span>

<span class="nc" id="L78">            long ops = totalOps.get();</span>
<span class="nc" id="L79">            double avgLatencyMs = (totalLatencyNs.get() / 1_000_000.0) / ops;</span>

<span class="nc" id="L81">            System.out.println(&quot;Load test complete.&quot;);</span>
<span class="nc" id="L82">            System.out.println(&quot;Total operations: &quot; + ops);</span>
<span class="nc" id="L83">            System.out.printf(&quot;Average latency: %.2f ms%n&quot;, avgLatencyMs);</span>
<span class="nc" id="L84">            System.out.printf(&quot;Throughput: %.2f ops/sec%n&quot;, ops / (double) TEST_DURATION_SECONDS);</span>
<span class="nc" id="L85">        }</span>
<span class="nc" id="L86">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>