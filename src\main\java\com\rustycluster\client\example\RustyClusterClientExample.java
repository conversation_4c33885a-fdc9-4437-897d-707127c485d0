package com.rustycluster.client.example;

import com.rustycluster.client.BatchOperationBuilder;
import com.rustycluster.client.RustyClusterClient;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.RustyClusterProto;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Example usage of the RustyClusterClient.
 */
public class RustyClusterClientExample {

    public static void main(String[] args) {

        // Use the third configuration for this example (3 nodes with automatic role assignment)
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051", "localhost:50052", "localhost:50053")
                .maxConnectionsPerNode(5)
                .connectionTimeout(5, TimeUnit.SECONDS)
                .readTimeout(3, TimeUnit.SECONDS)
                .writeTimeout(3, TimeUnit.SECONDS)
                .maxRetries(3)
                .retryDelay(500, TimeUnit.MILLISECONDS)
                .authentication("testuser", "testpass")  // Add authentication credentials
                .build();

        // Create client
        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Authenticate with the server
            System.out.println("Authenticating with RustyCluster server...");
            boolean authResult = client.authenticate();
            System.out.println("Authentication result: " + authResult);

            if (!authResult) {
                System.err.println("Authentication failed! Exiting...");
                return;
            }

            System.out.println("Authentication successful! Session token: " + client.getSessionToken());
 
            // String operations
            System.out.println("Setting key 'greeting' to 'Hello, RustyCluster!'");
            boolean setResult = client.set("greeting", "Hello, RustyCluster!");
            System.out.println("Set result: " + setResult);

            System.out.println("Getting key 'greeting'");
            String value = client.get("greeting");
            System.out.println("Value: " + value);

            System.out.println("Setting key 'temp' with expiry");
            boolean setExResult = client.setEx("temp", "This will expire", 60);
            System.out.println("SetEx result: " + setExResult);

            // Numeric operations
            System.out.println("Incrementing key 'counter'");
            long counterValue = client.incrBy("counter", 1);
            System.out.println("Counter value: " + counterValue);

            System.out.println("Incrementing counter by 10");
            counterValue = client.incrBy("counter", 10);
            System.out.println("Counter value: " + counterValue);

            System.out.println("Decrementing counter by 5");
            counterValue = client.decrBy("counter", 5);
            System.out.println("Counter value: " + counterValue);

            // Hash operations
            System.out.println("Setting hash field");
            boolean hSetResult = client.hSet("user:1", "name", "John Doe");
            System.out.println("HSet result: " + hSetResult);

            client.hSet("user:1", "email", "<EMAIL>");
            client.hSet("user:1", "age", "30");

            System.out.println("Getting hash field");
            String name = client.hGet("user:1", "name");
            System.out.println("Name: " + name);

            System.out.println("Getting all hash fields");
            Map<String, String> userFields = client.hGetAll("user:1");
            System.out.println("User fields: " + userFields);

            System.out.println("Incrementing hash field");
            long age = client.hIncrBy("user:1", "age", 1);
            System.out.println("New age: " + age);

            // Batch operations
            System.out.println("Executing batch operations");
            BatchOperationBuilder batchBuilder = new BatchOperationBuilder()
                    .addSet("batch1", "value1")
                    .addSet("batch2", "value2")
                    .addSetEx("batch3", "value3", 300)
                    .addHSet("batch:hash", "field1", "value1")
                    .addHSet("batch:hash", "field2", "value2");

            List<RustyClusterProto.BatchOperation> batchOperations = batchBuilder.build();
            List<Boolean> batchResults = client.batchWrite(batchOperations);
            System.out.println("Batch results: " + batchResults);

            // Verify batch results
            System.out.println("Verifying batch results");
            System.out.println("batch1: " + client.get("batch1"));
            System.out.println("batch2: " + client.get("batch2"));
            System.out.println("batch3: " + client.get("batch3"));
            System.out.println("batch:hash fields: " + client.hGetAll("batch:hash"));

            // Clean up
            /*System.out.println("Cleaning up");
            client.delete("greeting");
            client.delete("temp");
            client.delete("counter");
            client.delete("user:1");
            client.delete("batch1");
            client.delete("batch2");
            client.delete("batch3");
            client.delete("batch:hash");*/
        }
    }
}
