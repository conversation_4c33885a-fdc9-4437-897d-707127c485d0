<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterClientConfig.Builder</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">com.rustycluster.client.config</a> &gt; <span class="el_class">RustyClusterClientConfig.Builder</span></div><h1>RustyClusterClientConfig.Builder</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">16 of 366</td><td class="ctr2">95%</td><td class="bar">3 of 18</td><td class="ctr2">83%</td><td class="ctr1">3</td><td class="ctr2">34</td><td class="ctr1">4</td><td class="ctr2">90</td><td class="ctr1">0</td><td class="ctr2">25</td></tr></tfoot><tbody><tr><td id="a2"><a href="RustyClusterClientConfig.java.html#L147" class="el_method">addNodes(String[])</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="96" height="10" title="65" alt="65"/></td><td class="ctr2" id="c24">80%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="9" alt="9"/></td><td class="ctr2" id="e2">75%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h0">4</td><td class="ctr2" id="i0">21</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="RustyClusterClientConfig.java.html#L90" class="el_method">addNodes(NodeRole, String[])</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="82" height="10" title="56" alt="56"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">11</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a16"><a href="RustyClusterClientConfig.java.html#L289" class="el_method">highThroughputPreset()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="26" alt="26"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">7</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a17"><a href="RustyClusterClientConfig.java.html#L305" class="el_method">lowLatencyPreset()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="26" alt="26"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a10"><a href="RustyClusterClientConfig.java.html#L321" class="el_method">balancedPreset()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="26" alt="26"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="RustyClusterClientConfig.java.html#L45" class="el_method">addNode(String, int, NodeRole)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="13" alt="13"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a11"><a href="RustyClusterClientConfig.java.html#L370" class="el_method">build()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="13" alt="13"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i5">3</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a23"><a href="RustyClusterClientConfig.java.html#L264" class="el_method">useSecureConnection(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="10" alt="10"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a9"><a href="RustyClusterClientConfig.java.html#L277" class="el_method">authentication(String, String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="10" alt="10"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a22"><a href="RustyClusterClientConfig.java.html#L33" class="el_method">RustyClusterClientConfig.Builder()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="8" alt="8"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a12"><a href="RustyClusterClientConfig.java.html#L206" class="el_method">connectionTimeout(long, TimeUnit)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="8" alt="8"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a20"><a href="RustyClusterClientConfig.java.html#L218" class="el_method">readTimeout(long, TimeUnit)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="8" alt="8"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a24"><a href="RustyClusterClientConfig.java.html#L230" class="el_method">writeTimeout(long, TimeUnit)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="8" alt="8"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a21"><a href="RustyClusterClientConfig.java.html#L253" class="el_method">retryDelay(long, TimeUnit)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="8" alt="8"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a14"><a href="RustyClusterClientConfig.java.html#L349" class="el_method">failbackCheckInterval(long, TimeUnit)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="8" alt="8"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a3"><a href="RustyClusterClientConfig.java.html#L57" class="el_method">addPrimaryNode(String, int)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="6" alt="6"/></td><td class="ctr2" id="c14">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a5"><a href="RustyClusterClientConfig.java.html#L68" class="el_method">addSecondaryNode(String, int)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="6" alt="6"/></td><td class="ctr2" id="c15">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a7"><a href="RustyClusterClientConfig.java.html#L79" class="el_method">addTertiaryNode(String, int)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="6" alt="6"/></td><td class="ctr2" id="c16">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a18"><a href="RustyClusterClientConfig.java.html#L194" class="el_method">maxConnectionsPerNode(int)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="6" alt="6"/></td><td class="ctr2" id="c17">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a19"><a href="RustyClusterClientConfig.java.html#L241" class="el_method">maxRetries(int)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="6" alt="6"/></td><td class="ctr2" id="c18">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i16">2</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a13"><a href="RustyClusterClientConfig.java.html#L337" class="el_method">enableFailback(boolean)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="6" alt="6"/></td><td class="ctr2" id="c19">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i17">2</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a15"><a href="RustyClusterClientConfig.java.html#L360" class="el_method">failbackHealthCheckRetries(int)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="6" alt="6"/></td><td class="ctr2" id="c20">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i18">2</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a4"><a href="RustyClusterClientConfig.java.html#L114" class="el_method">addPrimaryNodes(String[])</a></td><td class="bar" id="b22"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="5" alt="5"/></td><td class="ctr2" id="c21">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a6"><a href="RustyClusterClientConfig.java.html#L124" class="el_method">addSecondaryNodes(String[])</a></td><td class="bar" id="b23"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="5" alt="5"/></td><td class="ctr2" id="c22">100%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">0</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">0</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">0</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a8"><a href="RustyClusterClientConfig.java.html#L134" class="el_method">addTertiaryNodes(String[])</a></td><td class="bar" id="b24"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="5" alt="5"/></td><td class="ctr2" id="c23">100%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">0</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">0</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">0</td><td class="ctr2" id="k24">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>