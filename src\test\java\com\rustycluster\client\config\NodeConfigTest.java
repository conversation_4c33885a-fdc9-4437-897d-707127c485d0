package com.rustycluster.client.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.assertj.core.api.Assertions.assertThat;

class NodeConfigTest {

    @Test
    @DisplayName("Should create a NodeConfig with the specified values")
    void shouldCreateNodeConfigWithSpecifiedValues() {
        // Given
        String host = "localhost";
        int port = 50051;
        NodeRole role = NodeRole.PRIMARY;

        // When
        NodeConfig nodeConfig = new NodeConfig(host, port, role);

        // Then
        assertThat(nodeConfig.host()).isEqualTo(host);
        assertThat(nodeConfig.port()).isEqualTo(port);
        assertThat(nodeConfig.role()).isEqualTo(role);
    }

    @Test
    @DisplayName("Should return the correct address")
    void shouldReturnCorrectAddress() {
        // Given
        NodeConfig nodeConfig = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);

        // When
        String address = nodeConfig.getAddress();

        // Then
        assertThat(address).isEqualTo("localhost:50051");
    }

    @Test
    @DisplayName("Should implement equals and hashCode correctly")
    void shouldImplementEqualsAndHashCodeCorrectly() {
        // Given
        NodeConfig nodeConfig1 = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);
        NodeConfig nodeConfig2 = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);
        NodeConfig nodeConfig3 = new NodeConfig("localhost", 50052, NodeRole.PRIMARY);

        // Then
        assertThat(nodeConfig1).isEqualTo(nodeConfig2);
        assertThat(nodeConfig1).isNotEqualTo(nodeConfig3);
        assertThat(nodeConfig1.hashCode()).isEqualTo(nodeConfig2.hashCode());
        assertThat(nodeConfig1.hashCode()).isNotEqualTo(nodeConfig3.hashCode());
    }

    @Test
    @DisplayName("Should implement toString correctly")
    void shouldImplementToStringCorrectly() {
        // Given
        NodeConfig nodeConfig = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);

        // When
        String toString = nodeConfig.toString();

        // Then
        assertThat(toString).contains("localhost");
        assertThat(toString).contains("50051");
        assertThat(toString).contains("PRIMARY");
    }
}
