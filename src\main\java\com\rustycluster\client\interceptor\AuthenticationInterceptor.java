package com.rustycluster.client.interceptor;

import com.rustycluster.client.auth.AuthenticationManager;
import io.grpc.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * gRPC client interceptor that automatically adds authentication headers to requests.
 */
public class AuthenticationInterceptor implements ClientInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthenticationInterceptor.class);
    private static final Metadata.Key<String> AUTHORIZATION_HEADER = 
            Metadata.Key.of("authorization", Metadata.ASCII_STRING_MARSHALLER);
    
    private final AuthenticationManager authenticationManager;
    
    public AuthenticationInterceptor(AuthenticationManager authenticationManager) {
        this.authenticationManager = authenticationManager;
    }
    
    @Override
    public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
            MethodDescriptor<ReqT, RespT> method,
            CallOptions callOptions,
            Channel next) {
        
        return new ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(
                next.newCall(method, callOptions)) {
            
            @Override
            public void start(Listener<RespT> responseListener, Metadata headers) {
                // Add authorization header if authenticated
                if (authenticationManager.isAuthenticated()) {
                    String sessionToken = authenticationManager.getSessionToken();
                    if (sessionToken != null) {
                        headers.put(AUTHORIZATION_HEADER, "Bearer " + sessionToken);
                        logger.debug("Added authorization header for method: {}", method.getFullMethodName());
                    }
                }
                
                super.start(responseListener, headers);
            }
        };
    }
}
