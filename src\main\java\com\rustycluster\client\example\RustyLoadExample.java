package com.rustycluster.client.example;

import com.rustycluster.client.RustyClusterClient;
import com.rustycluster.client.config.RustyClusterClientConfig;

import java.util.Random;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * A simple load test for Rusty<PERSON>lusterClient.
 */
public class RustyLoadExample {

    private static final int NUM_THREADS = 10;
    private static final int TEST_DURATION_SECONDS = 30;

    public static void main(String[] args) throws InterruptedException {
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051", "localhost:50052", "localhost:50053")
                .maxConnectionsPerNode(128)
                .connectionTimeout(5, TimeUnit.SECONDS)
                .readTimeout(3, TimeUnit.SECONDS)
                .writeTimeout(3, TimeUnit.SECONDS)
                .maxRetries(3)
                .retryDelay(500, TimeUnit.MILLISECONDS)
                .authentication("admin", "npci")
                .build();

        // Create client
        try (RustyClusterClient client = new RustyClusterClient(config)) {
            if (!client.authenticate()) {
                System.err.println("Authentication failed. Exiting...");
                return;
            }

            System.out.println("Authentication successful. Starting load test...");

            ExecutorService executor = Executors.newFixedThreadPool(NUM_THREADS);
            AtomicLong totalOps = new AtomicLong(0);
            AtomicLong totalLatencyNs = new AtomicLong(0);
            Random random = new Random();

            long endTime = System.nanoTime() + TimeUnit.SECONDS.toNanos(TEST_DURATION_SECONDS);

            Runnable worker = () -> {
                while (System.nanoTime() < endTime) {
                    String key = "key" + random.nextInt(1000);
                    String value = "val" + random.nextInt(1000);

                    long start = System.nanoTime();

                    // Randomly choose an operation
                    int op = random.nextInt(4);
                    try {
                        switch (op) {
                            case 0 -> client.set(key, value);
                            case 1 -> client.get(key);
                            case 2 -> client.incrBy("counter", 1);
                            case 3 -> client.hSet("hash:loadtest", "field" + random.nextInt(10), value);
                        }
                        long latency = System.nanoTime() - start;
                        totalOps.incrementAndGet();
                        totalLatencyNs.addAndGet(latency);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            };

            for (int i = 0; i < NUM_THREADS; i++) {
                executor.submit(worker);
            }

            executor.shutdown();
            executor.awaitTermination(TEST_DURATION_SECONDS + 5, TimeUnit.SECONDS);

            long ops = totalOps.get();
            double avgLatencyMs = (totalLatencyNs.get() / 1_000_000.0) / ops;

            System.out.println("Load test complete.");
            System.out.println("Total operations: " + ops);
            System.out.printf("Average latency: %.2f ms%n", avgLatencyMs);
            System.out.printf("Throughput: %.2f ops/sec%n", ops / (double) TEST_DURATION_SECONDS);
        }
    }
}
