<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AsyncConnectionManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.connection</a> &gt; <span class="el_source">AsyncConnectionManager.java</span></div><h1>AsyncConnectionManager.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.connection;

import com.google.common.util.concurrent.ListenableFuture;
import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.exception.NoAvailableNodesException;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages asynchronous connections to RustyCluster nodes, handling prioritization and failover.
 */
public class AsyncConnectionManager implements AutoCloseable {
<span class="nc" id="L24">    private static final Logger logger = LoggerFactory.getLogger(AsyncConnectionManager.class);</span>

    private final RustyClusterClientConfig config;
    private final AsyncConnectionPool connectionPool;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final List&lt;NodeConfig&gt; sortedNodes;
    private final Executor executor;
    private final AsyncFailbackManager failbackManager;

    /**
     * Create a new AsyncConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public AsyncConnectionManager(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
<span class="nc" id="L40">        this(config, new AsyncConnectionPool(config, authenticationManager));</span>
<span class="nc" id="L41">    }</span>

    /**
     * Create a new AsyncConnectionManager with a custom connection pool (for testing).
     *
     * @param config The client configuration
     * @param connectionPool The connection pool to use
     */
<span class="nc" id="L49">    AsyncConnectionManager(RustyClusterClientConfig config, AsyncConnectionPool connectionPool) {</span>
<span class="nc" id="L50">        this.config = config;</span>
<span class="nc" id="L51">        this.connectionPool = connectionPool;</span>
<span class="nc" id="L52">        this.executor = ForkJoinPool.commonPool(); // Use common pool for async operations</span>

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
<span class="nc" id="L55">        this.sortedNodes = config.getNodes().stream()</span>
<span class="nc" id="L56">                .sorted(Comparator.comparingInt(node -&gt; node.role().getPriority()))</span>
<span class="nc" id="L57">                .toList();</span>

        // Set the initial node to the highest priority node
<span class="nc" id="L60">        this.currentNode = new AtomicReference&lt;&gt;(sortedNodes.get(0));</span>

        // For async operations, we'll use a synchronous FailbackManager with the async pool
        // The health checks will be performed synchronously but the failback manager itself
        // runs in a separate thread, so it won't block async operations
<span class="nc" id="L65">        this.failbackManager = new AsyncFailbackManager(config, connectionPool, sortedNodes, currentNode);</span>
<span class="nc" id="L66">        this.failbackManager.start();</span>

<span class="nc" id="L68">        logger.info(&quot;AsyncConnectionManager initialized with {} nodes&quot;, sortedNodes.size());</span>
<span class="nc" id="L69">    }</span>

    /**
     * Execute an operation asynchronously with automatic failover.
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return CompletableFuture that completes with the result of the operation
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation) {
<span class="nc" id="L79">        return executeWithFailoverAsync(operation, OperationType.READ, 0);</span>
    }

    /**
     * Execute an operation asynchronously with automatic failover and specific timeout based on operation type.
     *
     * @param operation     The operation to execute
     * @param operationType The type of operation (READ, WRITE, AUTH) to determine appropriate timeout
     * @param &lt;T&gt;           The return type of the operation
     * @return CompletableFuture that completes with the result of the operation
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation, OperationType operationType) {
<span class="nc" id="L91">        return executeWithFailoverAsync(operation, operationType, 0);</span>
    }

    private &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation, OperationType operationType, int retryCount) {
<span class="nc bnc" id="L95" title="All 2 branches missed.">        if (retryCount &gt; config.getMaxRetries()) {</span>
<span class="nc" id="L96">            return CompletableFuture.failedFuture(</span>
                new NoAvailableNodesException(&quot;Operation failed after &quot; + retryCount + &quot; retries&quot;));
        }

        // Determine timeout based on operation type
<span class="nc bnc" id="L101" title="All 4 branches missed.">        long timeoutMs = switch (operationType) {</span>
<span class="nc" id="L102">            case READ -&gt; config.getReadTimeoutMs();</span>
<span class="nc" id="L103">            case WRITE -&gt; config.getWriteTimeoutMs();</span>
<span class="nc" id="L104">            case AUTH -&gt; config.getConnectionTimeoutMs();</span>
        };

<span class="nc" id="L107">        NodeConfig node = currentNode.get();</span>

<span class="nc" id="L109">        return connectionPool.borrowStubAsync(node)</span>
<span class="nc" id="L110">            .thenCompose(stub -&gt; {</span>
                try {
                    // Apply deadline per operation to avoid expired deadline issues
<span class="nc" id="L113">                    KeyValueServiceGrpc.KeyValueServiceFutureStub stubWithDeadline =</span>
<span class="nc" id="L114">                        stub.withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);</span>
<span class="nc" id="L115">                    ListenableFuture&lt;T&gt; listenableFuture = operation.execute(stubWithDeadline);</span>
<span class="nc" id="L116">                    return toCompletableFuture(listenableFuture)</span>
<span class="nc" id="L117">                        .whenComplete((result, throwable) -&gt; {</span>
<span class="nc" id="L118">                            connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L119">                        });</span>
<span class="nc" id="L120">                } catch (Exception e) {</span>
<span class="nc" id="L121">                    connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L122">                    return CompletableFuture.failedFuture(e);</span>
                }
            })
<span class="nc" id="L125">            .exceptionally(throwable -&gt; {</span>
<span class="nc" id="L126">                logger.warn(&quot;Operation failed on node {}: {}&quot;, node, throwable.getMessage());</span>

                // Try to find the next available node
<span class="nc" id="L129">                var nextNode = findNextAvailableNode(node);</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">                if (nextNode != null) {</span>
<span class="nc" id="L131">                    currentNode.set(nextNode);</span>
<span class="nc" id="L132">                    logger.info(&quot;Switched to node: {}&quot;, nextNode);</span>

                    // Clear authentication state when switching nodes
                    // This will force re-authentication on the next operation
<span class="nc bnc" id="L136" title="All 2 branches missed.">                    if (config.hasAuthentication()) {</span>
<span class="nc" id="L137">                        connectionPool.getAuthenticationManager().clearAuthentication();</span>
<span class="nc" id="L138">                        logger.debug(&quot;Cleared authentication state for node switch to: {}&quot;, nextNode);</span>
                    }
                }

<span class="nc" id="L142">                throw new RuntimeException(throwable);</span>
            })
<span class="nc" id="L144">            .handle((result, throwable) -&gt; {</span>
<span class="nc bnc" id="L145" title="All 2 branches missed.">                if (throwable != null) {</span>
                    // Retry with delay
<span class="nc" id="L147">                    CompletableFuture&lt;Void&gt; delay = new CompletableFuture&lt;&gt;();</span>
<span class="nc" id="L148">                    CompletableFuture.delayedExecutor(config.getRetryDelayMs(),</span>
                            java.util.concurrent.TimeUnit.MILLISECONDS, executor)
<span class="nc" id="L150">                        .execute(() -&gt; delay.complete(null));</span>
<span class="nc" id="L151">                    return delay.thenCompose(v -&gt; executeWithFailoverAsync(operation, operationType, retryCount + 1));</span>
                }
<span class="nc" id="L153">                return CompletableFuture.completedFuture(result);</span>
            })
<span class="nc bnc" id="L155" title="All 2 branches missed.">            .thenCompose(future -&gt; future instanceof CompletableFuture ?</span>
<span class="nc" id="L156">                (CompletableFuture&lt;T&gt;) future : CompletableFuture.completedFuture((T) future));</span>
    }

    /**
     * Convert ListenableFuture to CompletableFuture.
     */
    private &lt;T&gt; CompletableFuture&lt;T&gt; toCompletableFuture(ListenableFuture&lt;T&gt; listenableFuture) {
<span class="nc" id="L163">        CompletableFuture&lt;T&gt; completableFuture = new CompletableFuture&lt;&gt;();</span>

<span class="nc" id="L165">        listenableFuture.addListener(() -&gt; {</span>
            try {
<span class="nc" id="L167">                completableFuture.complete(listenableFuture.get());</span>
<span class="nc" id="L168">            } catch (Exception e) {</span>
<span class="nc" id="L169">                completableFuture.completeExceptionally(e);</span>
<span class="nc" id="L170">            }</span>
<span class="nc" id="L171">        }, executor);</span>

<span class="nc" id="L173">        return completableFuture;</span>
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none are available
     */
    private NodeConfig findNextAvailableNode(NodeConfig failedNode) {
        // First try to find a node with the same priority
<span class="nc" id="L184">        var samePriorityNode = sortedNodes.stream()</span>
<span class="nc bnc" id="L185" title="All 4 branches missed.">                .filter(node -&gt; node.role() == failedNode.role() &amp;&amp; !node.equals(failedNode))</span>
<span class="nc" id="L186">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L187">                .findFirst();</span>

<span class="nc bnc" id="L189" title="All 2 branches missed.">        if (samePriorityNode.isPresent()) {</span>
<span class="nc" id="L190">            return samePriorityNode.get();</span>
        }

        // Then try to find a node with lower priority
<span class="nc" id="L194">        var lowerPriorityNode = sortedNodes.stream()</span>
<span class="nc bnc" id="L195" title="All 2 branches missed.">                .filter(node -&gt; node.role().getPriority() &gt; failedNode.role().getPriority())</span>
<span class="nc" id="L196">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L197">                .findFirst();</span>

<span class="nc bnc" id="L199" title="All 2 branches missed.">        if (lowerPriorityNode.isPresent()) {</span>
<span class="nc" id="L200">            return lowerPriorityNode.get();</span>
        }

        // Finally, try any node except the failed one
<span class="nc" id="L204">        return sortedNodes.stream()</span>
<span class="nc bnc" id="L205" title="All 2 branches missed.">                .filter(node -&gt; !node.equals(failedNode))</span>
<span class="nc" id="L206">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L207">                .findFirst()</span>
<span class="nc" id="L208">                .orElse(null);</span>
    }

    /**
     * Check if a node is available.
     *
     * @param node The node to check
     * @return True if the node is available, false otherwise
     */
    private boolean isNodeAvailable(NodeConfig node) {
        try {
            // For async operations, we'll do a simple check
            // In a real implementation, you might want to perform an async health check
<span class="nc" id="L221">            return connectionPool.borrowStubAsync(node)</span>
<span class="nc" id="L222">                .thenApply(stub -&gt; {</span>
<span class="nc" id="L223">                    connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L224">                    return true;</span>
                })
<span class="nc" id="L226">                .exceptionally(e -&gt; {</span>
<span class="nc" id="L227">                    logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L228">                    return false;</span>
                })
<span class="nc" id="L230">                .join(); // This is not ideal for async, but needed for this interface</span>
<span class="nc" id="L231">        } catch (Exception e) {</span>
<span class="nc" id="L232">            logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L233">            return false;</span>
        }
    }



    /**
     * Close the connection manager and release all resources.
     */
    @Override
    public void close() {
<span class="nc" id="L244">        failbackManager.close();</span>
<span class="nc" id="L245">        connectionPool.close();</span>
<span class="nc" id="L246">        logger.info(&quot;AsyncConnectionManager closed&quot;);</span>
<span class="nc" id="L247">    }</span>

    /**
     * Functional interface for async client operations.
     *
     * @param &lt;T&gt; The return type of the operation
     */
    @FunctionalInterface
    public interface AsyncClientOperation&lt;T&gt; {
        /**
         * Execute an operation using the provided client stub.
         *
         * @param stub The client stub
         * @return ListenableFuture with the result of the operation
         * @throws Exception If the operation fails
         */
        ListenableFuture&lt;T&gt; execute(KeyValueServiceGrpc.KeyValueServiceFutureStub stub) throws Exception;
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>