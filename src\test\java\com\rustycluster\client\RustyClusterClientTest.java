package com.rustycluster.client;

import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.connection.ConnectionManager;
import com.rustycluster.client.connection.OperationType;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import com.rustycluster.grpc.RustyClusterProto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RustyClusterClientTest {

    @Mock
    private ConnectionManager connectionManager;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub stub;

    private RustyClusterClient client;

    @BeforeEach
    void setUp() throws Exception {
        // Create a test configuration
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051")
                .build();

        // Create a client with a mocked ConnectionManager
        client = new RustyClusterClient(config, connectionManager);

        // Default behavior for ConnectionManager (lenient to avoid unnecessary stubbing warnings)
        // Handle both the old single-parameter method and the new two-parameter method
        lenient().when(connectionManager.executeWithFailover(any())).thenAnswer(invocation -> {
            ConnectionManager.ClientOperation<?> operation = invocation.getArgument(0);
            return operation.execute(stub);
        });

        lenient().when(connectionManager.executeWithFailover(any(), any(OperationType.class))).thenAnswer(invocation -> {
            ConnectionManager.ClientOperation<?> operation = invocation.getArgument(0);
            return operation.execute(stub);
        });
    }

    @Test
    @DisplayName("Should set a key-value pair")
    void shouldSetKeyValuePair() {
        // Given
        RustyClusterProto.SetResponse response = RustyClusterProto.SetResponse.newBuilder().setSuccess(true).build();
        when(stub.set(any(RustyClusterProto.SetRequest.class))).thenReturn(response);

        // When
        boolean result = client.set("key", "value");

        // Then
        assertThat(result).isTrue();
        verify(stub).set(argThat(request ->
                request.getKey().equals("key") &&
                request.getValue().equals("value") &&
                !request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should set a key-value pair with skip replication")
    void shouldSetKeyValuePairWithSkipReplication() {
        // Given
        RustyClusterProto.SetResponse response = RustyClusterProto.SetResponse.newBuilder().setSuccess(true).build();
        when(stub.set(any(RustyClusterProto.SetRequest.class))).thenReturn(response);

        // When
        boolean result = client.set("key", "value", true);

        // Then
        assertThat(result).isTrue();
        verify(stub).set(argThat(request ->
                request.getKey().equals("key") &&
                request.getValue().equals("value") &&
                request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should get a value by key")
    void shouldGetValueByKey() {
        // Given
        RustyClusterProto.GetResponse response = RustyClusterProto.GetResponse.newBuilder()
                .setValue("value")
                .setFound(true)
                .build();
        when(stub.get(any(RustyClusterProto.GetRequest.class))).thenReturn(response);

        // When
        String result = client.get("key");

        // Then
        assertThat(result).isEqualTo("value");
        verify(stub).get(argThat(request -> request.getKey().equals("key")));
    }

    @Test
    @DisplayName("Should return null when key is not found")
    void shouldReturnNullWhenKeyNotFound() {
        // Given
        RustyClusterProto.GetResponse response = RustyClusterProto.GetResponse.newBuilder()
                .setFound(false)
                .build();
        when(stub.get(any(RustyClusterProto.GetRequest.class))).thenReturn(response);

        // When
        String result = client.get("key");

        // Then
        assertThat(result).isNull();
        verify(stub).get(argThat(request -> request.getKey().equals("key")));
    }

    @Test
    @DisplayName("Should delete a key")
    void shouldDeleteKey() {
        // Given
        RustyClusterProto.DeleteResponse response = RustyClusterProto.DeleteResponse.newBuilder().setSuccess(true).build();
        when(stub.delete(any(RustyClusterProto.DeleteRequest.class))).thenReturn(response);

        // When
        boolean result = client.delete("key");

        // Then
        assertThat(result).isTrue();
        verify(stub).delete(argThat(request ->
                request.getKey().equals("key") &&
                !request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should set a key with expiration")
    void shouldSetKeyWithExpiration() {
        // Given
        RustyClusterProto.SetExResponse response = RustyClusterProto.SetExResponse.newBuilder().setSuccess(true).build();
        when(stub.setEx(any(RustyClusterProto.SetExRequest.class))).thenReturn(response);

        // When
        boolean result = client.setEx("key", "value", 60);

        // Then
        assertThat(result).isTrue();
        verify(stub).setEx(argThat(request ->
                request.getKey().equals("key") &&
                request.getValue().equals("value") &&
                request.getTtl() == 60 &&
                !request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should increment a numeric value")
    void shouldIncrementNumericValue() {
        // Given
        RustyClusterProto.IncrByResponse response = RustyClusterProto.IncrByResponse.newBuilder().setNewValue(11).build();
        when(stub.incrBy(any(RustyClusterProto.IncrByRequest.class))).thenReturn(response);

        // When
        long result = client.incrBy("counter", 1);

        // Then
        assertThat(result).isEqualTo(11);
        verify(stub).incrBy(argThat(request ->
                request.getKey().equals("counter") &&
                request.getValue() == 1 &&
                !request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should get all hash fields")
    void shouldGetAllHashFields() {
        // Given
        RustyClusterProto.HGetAllResponse response = RustyClusterProto.HGetAllResponse.newBuilder()
                .putFields("name", "John")
                .putFields("age", "30")
                .build();
        when(stub.hGetAll(any(RustyClusterProto.HGetAllRequest.class))).thenReturn(response);

        // When
        Map<String, String> result = client.hGetAll("user:1");

        // Then
        assertThat(result).hasSize(2);
        assertThat(result).containsEntry("name", "John");
        assertThat(result).containsEntry("age", "30");
        verify(stub).hGetAll(argThat(request -> request.getKey().equals("user:1")));
    }

    @Test
    @DisplayName("Should execute batch operations")
    void shouldExecuteBatchOperations() {
        // Given
        RustyClusterProto.BatchWriteResponse response = RustyClusterProto.BatchWriteResponse.newBuilder()
                .setSuccess(true)
                .addAllOperationResults(List.of(true, true, true))
                .build();
        when(stub.batchWrite(any(RustyClusterProto.BatchWriteRequest.class))).thenReturn(response);

        // When
        List<RustyClusterProto.BatchOperation> operations = new BatchOperationBuilder()
                .addSet("key1", "value1")
                .addSet("key2", "value2")
                .addSetEx("key3", "value3", 60)
                .build();
        List<Boolean> results = client.batchWrite(operations);

        // Then
        assertThat(results).hasSize(3);
        assertThat(results).containsOnly(true);
        verify(stub).batchWrite(argThat(request ->
                request.getOperationsCount() == 3 &&
                !request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should close connection manager when closed")
    void shouldCloseConnectionManagerWhenClosed() {
        // When
        client.close();

        // Then
        verify(connectionManager).close();
    }
}
