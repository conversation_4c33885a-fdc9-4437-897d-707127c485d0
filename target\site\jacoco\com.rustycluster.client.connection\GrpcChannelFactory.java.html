<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GrpcChannelFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.connection</a> &gt; <span class="el_source">GrpcChannelFactory.java</span></div><h1>GrpcChannelFactory.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.connection;

import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.netty.shaded.io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.handler.ssl.SslContext;

import java.io.File;
import java.util.concurrent.TimeUnit;

/**
 * Factory for creating gRPC channels to RustyCluster nodes.
 */
public class GrpcChannelFactory {
    private final RustyClusterClientConfig config;

    /**
     * Create a new GrpcChannelFactory.
     *
     * @param config The client configuration
     */
<span class="nc" id="L25">    public GrpcChannelFactory(RustyClusterClientConfig config) {</span>
<span class="nc" id="L26">        this.config = config;</span>
<span class="nc" id="L27">    }</span>

    /**
     * Create a new gRPC channel for the given node.
     *
     * @param nodeConfig The node configuration
     * @return A new ManagedChannel instance
     */
    public ManagedChannel createChannel(NodeConfig nodeConfig) {
<span class="nc bnc" id="L36" title="All 2 branches missed.">        if (config.isUseSecureConnection()) {</span>
<span class="nc" id="L37">            return createSecureChannel(nodeConfig);</span>
        } else {
<span class="nc" id="L39">            return createInsecureChannel(nodeConfig);</span>
        }
    }

    private ManagedChannel createInsecureChannel(NodeConfig nodeConfig) {
<span class="nc" id="L44">        return ManagedChannelBuilder.forAddress(nodeConfig.host(), nodeConfig.port())</span>
<span class="nc" id="L45">                .usePlaintext()</span>
                // High-throughput keep-alive settings
<span class="nc" id="L47">                .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive</span>
<span class="nc" id="L48">                .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection</span>
<span class="nc" id="L49">                .keepAliveWithoutCalls(true)</span>
                // Performance optimizations
<span class="nc" id="L51">                .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches</span>
<span class="nc" id="L52">                .maxInboundMetadataSize(8 * 1024) // 8KB metadata</span>
                // Connection management
<span class="nc" id="L54">                .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections</span>
                // Retry configuration
<span class="nc" id="L56">                .enableRetry()</span>
<span class="nc" id="L57">                .maxRetryAttempts(3)</span>
<span class="nc" id="L58">                .build();</span>
    }

    private ManagedChannel createSecureChannel(NodeConfig nodeConfig) {
        try {
<span class="nc" id="L63">            SslContext sslContext = GrpcSslContexts.forClient()</span>
<span class="nc" id="L64">                    .trustManager(new File(config.getTlsCertPath()))</span>
<span class="nc" id="L65">                    .build();</span>

<span class="nc" id="L67">            return NettyChannelBuilder.forAddress(nodeConfig.host(), nodeConfig.port())</span>
<span class="nc" id="L68">                    .sslContext(sslContext)</span>
                    // High-throughput keep-alive settings
<span class="nc" id="L70">                    .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive</span>
<span class="nc" id="L71">                    .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection</span>
<span class="nc" id="L72">                    .keepAliveWithoutCalls(true)</span>
                    // Performance optimizations
<span class="nc" id="L74">                    .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches</span>
<span class="nc" id="L75">                    .maxInboundMetadataSize(8 * 1024) // 8KB metadata</span>
                    // Connection management
<span class="nc" id="L77">                    .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections</span>
                    // Retry configuration
<span class="nc" id="L79">                    .enableRetry()</span>
<span class="nc" id="L80">                    .maxRetryAttempts(3)</span>
<span class="nc" id="L81">                    .build();</span>
<span class="nc" id="L82">        } catch (Exception e) {</span>
<span class="nc" id="L83">            throw new RuntimeException(&quot;Failed to create secure channel&quot;, e);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>