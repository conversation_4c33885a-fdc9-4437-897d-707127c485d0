package com.rustycluster.client;

import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.NodeRole;
import com.rustycluster.client.config.RustyClusterClientConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Simple test to verify the test setup is working.
 */
class SimpleTest {

    @Test
    @DisplayName("Should verify test setup is working")
    void shouldVerifyTestSetupIsWorking() {
        // Given
        String expected = "Hello, RustyCluster!";

        // When
        String actual = "Hello, RustyCluster!";

        // Then
        assertThat(actual).isEqualTo(expected);
    }

    @Test
    @DisplayName("Should verify BatchOperationBuilder can be instantiated")
    void shouldVerifyBatchOperationBuilderCanBeInstantiated() {
        // Given/When
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // Then
        assertThat(builder).isNotNull();
    }

    @Test
    @DisplayName("Should verify NodeConfig record works correctly")
    void shouldVerifyNodeConfigRecordWorksCorrectly() {
        // Given
        NodeConfig nodeConfig = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);

        // Then
        assertThat(nodeConfig.host()).isEqualTo("localhost");
        assertThat(nodeConfig.port()).isEqualTo(50051);
        assertThat(nodeConfig.role()).isEqualTo(NodeRole.PRIMARY);
        assertThat(nodeConfig.getAddress()).isEqualTo("localhost:50051");
    }

    @Test
    @DisplayName("Should verify RustyClusterClientConfig can be built")
    void shouldVerifyRustyClusterClientConfigCanBeBuilt() {
        // Given/When
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051", "localhost:50052")
                .maxConnectionsPerNode(5)
                .connectionTimeout(10, TimeUnit.SECONDS)
                .build();

        // Then
        assertThat(config.getNodes()).hasSize(2);
        assertThat(config.getNodes().get(0).role()).isEqualTo(NodeRole.PRIMARY);
        assertThat(config.getNodes().get(1).role()).isEqualTo(NodeRole.SECONDARY);
        assertThat(config.getMaxConnectionsPerNode()).isEqualTo(5);
        assertThat(config.getConnectionTimeoutMs()).isEqualTo(10000);
    }
}
