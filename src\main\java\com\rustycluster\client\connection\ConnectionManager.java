package com.rustycluster.client.connection;

import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.exception.NoAvailableNodesException;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages connections to RustyCluster nodes, handling prioritization and failover.
 */
public class ConnectionManager implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionManager.class);

    private final RustyClusterClientConfig config;
    private final ConnectionPool connectionPool;
    private final AtomicReference<NodeConfig> currentNode;
    private final List<NodeConfig> sortedNodes;
    private final FailbackManager failbackManager;

    /**
     * Create a new ConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public ConnectionManager(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
        this(config, new ConnectionPool(config, authenticationManager));
    }

    /**
     * Create a new ConnectionManager with a custom connection pool (for testing).
     *
     * @param config The client configuration
     * @param connectionPool The connection pool to use
     */
    ConnectionManager(RustyClusterClientConfig config, ConnectionPool connectionPool) {
        this.config = config;
        this.connectionPool = connectionPool;

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
        this.sortedNodes = config.getNodes().stream()
                .sorted(Comparator.comparingInt(node -> node.role().getPriority()))
                .toList();

        // Set the initial node to the highest priority node
        this.currentNode = new AtomicReference<>(sortedNodes.get(0));

        // Initialize failback manager
        this.failbackManager = new FailbackManager(config, connectionPool, sortedNodes, currentNode);
        this.failbackManager.start();

        logger.info("ConnectionManager initialized with {} nodes", sortedNodes.size());
    }

    /**
     * Execute an operation with automatic failover.
     *
     * @param operation The operation to execute
     * @param <T>       The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available
     */
    public <T> T executeWithFailover(ClientOperation<T> operation) throws NoAvailableNodesException {
        return executeWithFailover(operation, OperationType.READ);
    }

    /**
     * Execute an operation with automatic failover and specific timeout based on operation type.
     *
     * @param operation     The operation to execute
     * @param operationType The type of operation (READ, WRITE, AUTH) to determine appropriate timeout
     * @param <T>           The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available
     */
    public <T> T executeWithFailover(ClientOperation<T> operation, OperationType operationType) throws NoAvailableNodesException {
        int retries = 0;
        Exception lastException = null;

        // Determine timeout based on operation type
        long timeoutMs = switch (operationType) {
            case READ -> config.getReadTimeoutMs();
            case WRITE -> config.getWriteTimeoutMs();
            case AUTH -> config.getConnectionTimeoutMs();
        };

        while (retries <= config.getMaxRetries()) {
            NodeConfig node = currentNode.get();
            KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = null;

            try {
                stub = connectionPool.borrowStub(node);
                // Apply deadline per operation to avoid expired deadline issues
                KeyValueServiceGrpc.KeyValueServiceBlockingStub stubWithDeadline =
                    stub.withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);
                return operation.execute(stubWithDeadline);
            } catch (Exception e) {
                lastException = e;
                logger.warn("Operation failed on node {}: {}", node, e.getMessage());

                // Try to find the next available node
                var nextNode = findNextAvailableNode(node);
                if (nextNode != null) {
                    currentNode.set(nextNode);
                    logger.info("Switched to node: {}", nextNode);

                    // Clear authentication state when switching nodes
                    // This will force re-authentication on the next operation
                    if (config.hasAuthentication()) {
                        connectionPool.getAuthenticationManager().clearAuthentication();
                        logger.debug("Cleared authentication state for node switch to: {}", nextNode);
                    }
                } else {
                    logger.warn("No available nodes found after failure");
                }

                retries++;

                if (retries <= config.getMaxRetries()) {
                    try {
                        Thread.sleep(config.getRetryDelayMs());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted during retry delay", ie);
                    }
                }
            } finally {
                if (stub != null) {
                    connectionPool.returnStub(node, stub);
                }
            }
        }

        throw new NoAvailableNodesException("Operation failed after " + retries + " retries", lastException);
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none are available
     */
    private NodeConfig findNextAvailableNode(NodeConfig failedNode) {
        // First try to find a node with the same priority
        var samePriorityNode = sortedNodes.stream()
                .filter(node -> node.role() == failedNode.role() && !node.equals(failedNode))
                .filter(this::isNodeAvailable)
                .findFirst();

        if (samePriorityNode.isPresent()) {
            return samePriorityNode.get();
        }

        // Then try to find a node with lower priority
        var lowerPriorityNode = sortedNodes.stream()
                .filter(node -> node.role().getPriority() > failedNode.role().getPriority())
                .filter(this::isNodeAvailable)
                .findFirst();

        if (lowerPriorityNode.isPresent()) {
            return lowerPriorityNode.get();
        }

        // Finally, try any node except the failed one
        return sortedNodes.stream()
                .filter(node -> !node.equals(failedNode))
                .filter(this::isNodeAvailable)
                .findFirst()
                .orElse(null);
    }

    /**
     * Check if a node is available.
     *
     * @param node The node to check
     * @return True if the node is available, false otherwise
     */
    private boolean isNodeAvailable(NodeConfig node) {
        KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = null;
        try {
            stub = connectionPool.borrowStub(node);
            // In a real implementation, you might want to perform a health check
            return true;
        } catch (Exception e) {
            logger.warn("Node {} is not available: {}", node, e.getMessage());
            return false;
        } finally {
            if (stub != null) {
                connectionPool.returnStub(node, stub);
            }
        }
    }



    /**
     * Close the connection manager and release all resources.
     */
    @Override
    public void close() {
        failbackManager.close();
        connectionPool.close();
        logger.info("ConnectionManager closed");
    }

    /**
     * Functional interface for client operations.
     *
     * @param <T> The return type of the operation
     */
    @FunctionalInterface
    public interface ClientOperation<T> {
        /**
         * Execute an operation using the provided client stub.
         *
         * @param stub The client stub
         * @return The result of the operation
         * @throws Exception If the operation fails
         */
        T execute(KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) throws Exception;
    }
}
