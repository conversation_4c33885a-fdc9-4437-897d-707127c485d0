package com.rustycluster.client;

import com.rustycluster.grpc.RustyClusterProto;

import java.util.ArrayList;
import java.util.List;

/**
 * Builder for creating batch operations.
 */
public class BatchOperationBuilder {
    private final List<RustyClusterProto.BatchOperation> operations = new ArrayList<>();

    /**
     * Create a new BatchOperationBuilder.
     */
    public BatchOperationBuilder() {
    }

    /**
     * Add a SET operation to the batch.
     *
     * @param key   The key
     * @param value The value
     * @return The builder instance
     */
    public BatchOperationBuilder addSet(String key, String value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SET)
                .setKey(key)
                .setValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add a DELETE operation to the batch.
     *
     * @param key The key
     * @return The builder instance
     */
    public BatchOperationBuilder addDelete(String key) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.DELETE)
                .setKey(key)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add a SETEX operation to the batch.
     *
     * @param key   The key
     * @param value The value
     * @param ttl   The time-to-live in seconds
     * @return The builder instance
     */
    public BatchOperationBuilder addSetEx(String key, String value, long ttl) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SETEX)
                .setKey(key)
                .setValue(value)
                .setTtl(ttl)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add a SETEXPIRY operation to the batch.
     *
     * @param key The key
     * @param ttl The time-to-live in seconds
     * @return The builder instance
     */
    public BatchOperationBuilder addSetExpiry(String key, long ttl) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SETEXPIRY)
                .setKey(key)
                .setTtl(ttl)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an INCRBY operation to the batch.
     *
     * @param key   The key
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addIncrBy(String key, long value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.INCRBY)
                .setKey(key)
                .setIntValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add a DECRBY operation to the batch.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The builder instance
     */
    public BatchOperationBuilder addDecrBy(String key, long value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.DECRBY)
                .setKey(key)
                .setIntValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an INCRBYFLOAT operation to the batch.
     *
     * @param key   The key
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addIncrByFloat(String key, double value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.INCRBYFLOAT)
                .setKey(key)
                .setFloatValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an HSET operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return The builder instance
     */
    public BatchOperationBuilder addHSet(String key, String field, String value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HSET)
                .setKey(key)
                .setField(field)
                .setValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an HINCRBY operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addHIncrBy(String key, String field, long value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HINCRBY)
                .setKey(key)
                .setField(field)
                .setIntValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an HDECRBY operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The decrement value
     * @return The builder instance
     */
    public BatchOperationBuilder addHDecrBy(String key, String field, long value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HDECRBY)
                .setKey(key)
                .setField(field)
                .setIntValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an HINCRBYFLOAT operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addHIncrByFloat(String key, String field, double value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HINCRBYFLOAT)
                .setKey(key)
                .setField(field)
                .setFloatValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Build the list of batch operations.
     *
     * @return The list of batch operations
     */
    public List<RustyClusterProto.BatchOperation> build() {
        return new ArrayList<>(operations);
    }
}
