<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterProto.BatchOperation.OperationType</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">com.rustycluster.grpc</a> &gt; <span class="el_class">RustyClusterProto.BatchOperation.OperationType</span></div><h1>RustyClusterProto.BatchOperation.OperationType</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">55 of 186</td><td class="ctr2">70%</td><td class="bar">8 of 20</td><td class="ctr2">60%</td><td class="ctr1">11</td><td class="ctr2">25</td><td class="ctr1">14</td><td class="ctr2">46</td><td class="ctr1">6</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a8"><a href="RustyClusterProto.java.html#L19377" class="el_method">valueOf(Descriptors.EnumValueDescriptor)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="20" alt="20"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h0">5</td><td class="ctr2" id="i2">5</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a4"><a href="RustyClusterProto.java.html#L19358" class="el_method">getValueDescriptor()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="15" alt="15"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h1">3</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="RustyClusterProto.java.html#L19370" class="el_method">getDescriptor()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a3"><a href="RustyClusterProto.java.html#L19306" class="el_method">getNumber()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="c3">54%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="1" alt="1"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i4">3</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a9"><a href="RustyClusterProto.java.html#L19320" class="el_method">valueOf(int)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="RustyClusterProto.java.html#L19328" class="el_method">forNumber(int)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="24" alt="24"/></td><td class="ctr2" id="c2">92%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="110" height="10" title="11" alt="11"/></td><td class="ctr2" id="e0">91%</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g0">12</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i1">13</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="RustyClusterProto.java.html#L19346" class="el_method">internalGetValueMap()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="RustyClusterProto.java.html#L19366" class="el_method">getDescriptorForType()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="RustyClusterProto.java.html#L19210" class="el_method">static {...}</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="93" alt="93"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i0">15</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a6"><a href="RustyClusterProto.java.html#L19389" class="el_method">RustyClusterProto.BatchOperation.OperationType(String, int, int)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="8" alt="8"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i5">3</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>