package com.rustycluster.client.metrics;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * Performance metrics collection for RustyCluster client operations.
 * This class provides thread-safe metrics collection for monitoring high-throughput performance.
 */
public class PerformanceMetrics {
    private static final Logger logger = LoggerFactory.getLogger(PerformanceMetrics.class);
    
    private static final PerformanceMetrics INSTANCE = new PerformanceMetrics();
    
    // Operation counters
    private final LongAdder totalOperations = new LongAdder();
    private final LongAdder successfulOperations = new LongAdder();
    private final LongAdder failedOperations = new LongAdder();
    private final LongAdder retryOperations = new LongAdder();
    
    // Latency tracking
    private final AtomicLong totalLatencyNanos = new AtomicLong();
    private final AtomicLong minLatencyNanos = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong maxLatencyNanos = new AtomicLong();
    
    // Operation type counters
    private final ConcurrentHashMap<String, LongAdder> operationTypeCounters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> operationTypeLatencies = new ConcurrentHashMap<>();
    
    // Connection pool metrics
    private final LongAdder connectionPoolBorrows = new LongAdder();
    private final LongAdder connectionPoolReturns = new LongAdder();
    private final LongAdder connectionPoolCreations = new LongAdder();
    private final LongAdder connectionPoolDestructions = new LongAdder();
    
    // Failover metrics
    private final LongAdder failoverEvents = new LongAdder();
    private final ConcurrentHashMap<String, LongAdder> nodeFailures = new ConcurrentHashMap<>();
    
    // Timing
    private final Instant startTime = Instant.now();
    
    private PerformanceMetrics() {
        // Private constructor for singleton
    }
    
    public static PerformanceMetrics getInstance() {
        return INSTANCE;
    }
    
    /**
     * Record a successful operation with its latency.
     */
    public void recordOperation(String operationType, Duration latency) {
        totalOperations.increment();
        successfulOperations.increment();
        
        long latencyNanos = latency.toNanos();
        totalLatencyNanos.addAndGet(latencyNanos);
        
        // Update min/max latency
        minLatencyNanos.updateAndGet(current -> Math.min(current, latencyNanos));
        maxLatencyNanos.updateAndGet(current -> Math.max(current, latencyNanos));
        
        // Update operation type metrics
        operationTypeCounters.computeIfAbsent(operationType, k -> new LongAdder()).increment();
        operationTypeLatencies.computeIfAbsent(operationType, k -> new AtomicLong()).addAndGet(latencyNanos);
    }
    
    /**
     * Record a failed operation.
     */
    public void recordFailedOperation(String operationType) {
        totalOperations.increment();
        failedOperations.increment();
        operationTypeCounters.computeIfAbsent(operationType + "_FAILED", k -> new LongAdder()).increment();
    }
    
    /**
     * Record a retry operation.
     */
    public void recordRetry(String operationType) {
        retryOperations.increment();
        operationTypeCounters.computeIfAbsent(operationType + "_RETRY", k -> new LongAdder()).increment();
    }
    
    /**
     * Record connection pool activity.
     */
    public void recordConnectionPoolBorrow() {
        connectionPoolBorrows.increment();
    }
    
    public void recordConnectionPoolReturn() {
        connectionPoolReturns.increment();
    }
    
    public void recordConnectionPoolCreation() {
        connectionPoolCreations.increment();
    }
    
    public void recordConnectionPoolDestruction() {
        connectionPoolDestructions.increment();
    }
    
    /**
     * Record a failover event.
     */
    public void recordFailover(String fromNode, String toNode) {
        failoverEvents.increment();
        nodeFailures.computeIfAbsent(fromNode, k -> new LongAdder()).increment();
        logger.info("Failover recorded: {} -> {}", fromNode, toNode);
    }
    
    /**
     * Get current performance statistics.
     */
    public PerformanceStats getStats() {
        long total = totalOperations.sum();
        long successful = successfulOperations.sum();
        long failed = failedOperations.sum();
        long retries = retryOperations.sum();
        
        double successRate = total > 0 ? (double) successful / total * 100 : 0;
        double avgLatencyMs = successful > 0 ? 
            (double) totalLatencyNanos.get() / successful / 1_000_000 : 0;
        double minLatencyMs = minLatencyNanos.get() != Long.MAX_VALUE ? 
            (double) minLatencyNanos.get() / 1_000_000 : 0;
        double maxLatencyMs = (double) maxLatencyNanos.get() / 1_000_000;
        
        Duration uptime = Duration.between(startTime, Instant.now());
        double throughputOpsPerSec = total > 0 ? 
            (double) total / uptime.toSeconds() : 0;
        
        return new PerformanceStats(
            total, successful, failed, retries, successRate,
            avgLatencyMs, minLatencyMs, maxLatencyMs, throughputOpsPerSec,
            connectionPoolBorrows.sum(), connectionPoolReturns.sum(),
            connectionPoolCreations.sum(), connectionPoolDestructions.sum(),
            failoverEvents.sum(), uptime
        );
    }
    
    /**
     * Reset all metrics.
     */
    public void reset() {
        totalOperations.reset();
        successfulOperations.reset();
        failedOperations.reset();
        retryOperations.reset();
        totalLatencyNanos.set(0);
        minLatencyNanos.set(Long.MAX_VALUE);
        maxLatencyNanos.set(0);
        operationTypeCounters.clear();
        operationTypeLatencies.clear();
        connectionPoolBorrows.reset();
        connectionPoolReturns.reset();
        connectionPoolCreations.reset();
        connectionPoolDestructions.reset();
        failoverEvents.reset();
        nodeFailures.clear();
        logger.info("Performance metrics reset");
    }
    
    /**
     * Log current performance statistics.
     */
    public void logStats() {
        PerformanceStats stats = getStats();
        logger.info("Performance Stats: {} total ops, {:.2f}% success rate, {:.2f}ms avg latency, {:.2f} ops/sec throughput",
            stats.totalOperations(), stats.successRate(), stats.avgLatencyMs(), stats.throughputOpsPerSec());
    }
    
    /**
     * Performance statistics record.
     */
    public record PerformanceStats(
        long totalOperations,
        long successfulOperations,
        long failedOperations,
        long retryOperations,
        double successRate,
        double avgLatencyMs,
        double minLatencyMs,
        double maxLatencyMs,
        double throughputOpsPerSec,
        long connectionPoolBorrows,
        long connectionPoolReturns,
        long connectionPoolCreations,
        long connectionPoolDestructions,
        long failoverEvents,
        Duration uptime
    ) {}
}
