# RustyCluster Java Client

A Java client library for interacting with Rusty<PERSON><PERSON>, a distributed key-value store built in Rust. Built with Java 17 and modern language features.

## Features

- **High Availability**: Connect to multiple RustyCluster nodes with primary, secondary, and tertiary prioritization
- **Connection Pooling**: Efficient connection management with configurable pool sizes
- **Automatic Failover**: Seamlessly switch to backup nodes when the primary node is unavailable
- **Comprehensive API**: Support for all RustyCluster operations including string, numeric, hash, and batch operations
- **Configurable Retries**: Automatic retry mechanism with configurable delays and limits
- **TLS Support**: Optional secure connections with TLS
- **Authentication**: Username/password authentication with session token management

## Requirements

- Java 17 or higher
- Maven 3.6 or higher (for building from source)

## Installation

### Maven

Add the following dependency to your `pom.xml`:

```xml
<dependency>
    <groupId>com.rustycluster</groupId>
    <artifactId>rustycluster-java-client</artifactId>
    <version>1.0.0</version>
</dependency>
```

### Gradle

Add the following dependency to your `build.gradle`:

```groovy
implementation 'com.rustycluster:rustycluster-java-client:1.0.0'
```

## Quick Start

```java
import com.rustycluster.client.RustyClusterClient;
import com.rustycluster.client.config.RustyClusterClientConfig;

import java.util.concurrent.TimeUnit;

public class QuickStart {
    public static void main(String[] args) {
        // Create client configuration
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .addSecondaryNode("localhost", 50052)
                .maxConnectionsPerNode(5)
                .connectionTimeout(5, TimeUnit.SECONDS)
                .authentication("username", "password")  // Optional authentication
                .build();

        // Create client
        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Set a key-value pair
            client.set("greeting", "Hello, RustyCluster!");

            // Get a value
            String value = client.get("greeting");
            System.out.println(value);  // Output: Hello, RustyCluster!

            // Delete a key
            client.delete("greeting");
        }
    }
}
```

## Configuration

The client can be configured with various options and supports flexible node configuration:

### Basic Configuration

```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        // Add nodes with priorities
        .addPrimaryNode("primary.example.com", 50051)
        .addSecondaryNode("secondary1.example.com", 50051)
        .addTertiaryNode("tertiary.example.com", 50051)

        // Connection pool settings
        .maxConnectionsPerNode(10)

        // Timeouts
        .connectionTimeout(5, TimeUnit.SECONDS)
        .readTimeout(3, TimeUnit.SECONDS)
        .writeTimeout(3, TimeUnit.SECONDS)

        // Retry settings
        .maxRetries(3)
        .retryDelay(500, TimeUnit.MILLISECONDS)

        // Optional TLS configuration
        .useSecureConnection("/path/to/certificate.pem")

        // Optional authentication
        .authentication("username", "password")

        .build();
```

### Simplified Node Configuration

The client supports automatic role assignment based on the order nodes are added:

```java
// Single node (automatically PRIMARY)
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNodes("localhost:50051")
        .build();

// Two nodes (first is PRIMARY, second is SECONDARY)
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNodes("localhost:50051", "localhost:50052")
        .build();

// Three or more nodes (first is PRIMARY, second is SECONDARY, rest are TERTIARY)
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNodes("localhost:50051", "localhost:50052", "localhost:50053", "localhost:50054")
        .build();
```

### Advanced Node Configuration

For more control, you can explicitly specify node roles:

```java
// Add multiple nodes of the same role using string format "host:port"
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addPrimaryNodes("primary1.example.com:50051", "primary2.example.com:50051")
        .addSecondaryNodes("secondary1.example.com:50051", "secondary2.example.com:50051")
        .addTertiaryNodes("tertiary1.example.com:50051", "tertiary2.example.com:50051")
        .build();

// Add nodes with custom roles
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNode("node1.example.com", 50051, NodeRole.PRIMARY)
        .addNode("node2.example.com", 50051, NodeRole.SECONDARY)
        .addNodes(NodeRole.TERTIARY, "node3.example.com:50051", "node4.example.com:50051")
        .build();

// Mix automatic and explicit role assignment
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNodes("localhost:50051", "localhost:50052") // PRIMARY and SECONDARY
        .addTertiaryNodes("localhost:50053", "localhost:50054") // Additional TERTIARY nodes
        .build();
```

## Authentication

The RustyCluster Java client supports username/password authentication with session token management. When authentication is configured, the client will automatically authenticate with the server and include session tokens in subsequent requests.

### Configuring Authentication

```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNodes("localhost:50051", "localhost:50052")
        .authentication("username", "password")
        .build();
```

### Using Authentication

```java
try (RustyClusterClient client = new RustyClusterClient(config)) {
    // Authenticate with the server (optional - done automatically on first operation)
    boolean authResult = client.authenticate();
    if (authResult) {
        System.out.println("Authentication successful");

        // Perform operations - session token is automatically included
        client.set("key", "value");
        String value = client.get("key");
    } else {
        System.err.println("Authentication failed");
    }
}
```

### Authentication Features

- **Automatic Authentication**: The client automatically authenticates on the first operation if credentials are configured
- **Session Token Management**: Session tokens are automatically managed and included in all requests
- **Secure Token Storage**: Session tokens are stored securely in memory using atomic references
- **Authentication State Tracking**: The client tracks authentication state and can re-authenticate if needed
- **Interceptor Integration**: Authentication headers are automatically added to gRPC calls via interceptors

### Authentication Methods

```java
// Check if authentication is configured
boolean hasAuth = config.hasAuthentication();

// Manually authenticate (usually not needed)
boolean success = client.authenticate();

// Check authentication state
boolean isAuthenticated = authenticationManager.isAuthenticated();

// Get current session token (for debugging)
String token = authenticationManager.getSessionToken();

// Clear authentication state
authenticationManager.clearAuthentication();
```

## API Reference

### String Operations

```java
// Set a key-value pair
boolean set(String key, String value);
boolean set(String key, String value, boolean skipReplication);

// Get a value
String get(String key);

// Delete a key
boolean delete(String key);
boolean delete(String key, boolean skipReplication);

// Set with expiration
boolean setEx(String key, String value, long ttl);
boolean setEx(String key, String value, long ttl, boolean skipReplication);

// Set expiration on existing key
boolean setExpiry(String key, long ttl);
boolean setExpiry(String key, long ttl, boolean skipReplication);
```

### Numeric Operations

```java
// Increment a value
long incrBy(String key, long value);
long incrBy(String key, long value, boolean skipReplication);

// Decrement a value
long decrBy(String key, long value);
long decrBy(String key, long value, boolean skipReplication);

// Increment a float value
double incrByFloat(String key, double value);
double incrByFloat(String key, double value, boolean skipReplication);
```

### Hash Operations

```java
// Set a hash field
boolean hSet(String key, String field, String value);
boolean hSet(String key, String field, String value, boolean skipReplication);

// Get a hash field
String hGet(String key, String field);

// Get all hash fields
Map<String, String> hGetAll(String key);

// Increment a hash field
long hIncrBy(String key, String field, long value);
long hIncrBy(String key, String field, long value, boolean skipReplication);

// Decrement a hash field
long hDecrBy(String key, String field, long value);
long hDecrBy(String key, String field, long value, boolean skipReplication);

// Increment a float hash field
double hIncrByFloat(String key, String field, double value);
double hIncrByFloat(String key, String field, double value, boolean skipReplication);
```

### Batch Operations

```java
// Create batch operations
BatchOperationBuilder batchBuilder = new BatchOperationBuilder()
        .addSet("key1", "value1")
        .addSet("key2", "value2")
        .addSetEx("key3", "value3", 300)
        .addHSet("hash1", "field1", "value1");

// Execute batch
List<BatchOperation> operations = batchBuilder.build();
List<Boolean> results = client.batchWrite(operations);
```

## Error Handling

The client throws `NoAvailableNodesException` when all nodes are unavailable after the configured number of retries.

```java
try {
    client.set("key", "value");
} catch (NoAvailableNodesException e) {
    System.err.println("All RustyCluster nodes are unavailable: " + e.getMessage());
}
```

## Java 17 Features

This client library takes advantage of modern Java 17 features:

- **Records**: Immutable data classes with built-in equals, hashCode, and toString methods
- **Pattern Matching**: Simplified instanceof checks and type casting
- **Enhanced Switch Expressions**: More concise and powerful switch statements
- **Text Blocks**: Multi-line string literals for improved readability
- **Sealed Classes**: Restricting which classes can extend or implement a class or interface
- **Stream API Enhancements**: New methods like toList() for more concise code

## Testing

The client library includes comprehensive test coverage:

### Running Tests

```bash
# Run all tests
mvn test

# Run tests with coverage report
mvn test jacoco:report

# Run specific test class
mvn test -Dtest=RustyClusterClientConfigTest

# Run tests in a specific package
mvn test -Dtest="com.rustycluster.client.config.*"
```

### Test Categories

1. **Configuration Tests** (`RustyClusterClientConfigTest`)
   - Tests for node configuration with automatic role assignment
   - Tests for explicit role assignment
   - Tests for validation and error handling

2. **Node Configuration Tests** (`NodeConfigTest`, `NodeRoleTest`)
   - Tests for NodeConfig record functionality
   - Tests for NodeRole priority ordering

3. **Connection Management Tests** (`ConnectionManagerTest`)
   - Tests for failover behavior
   - Tests for retry mechanisms
   - Tests for connection pool management

4. **Client API Tests** (`RustyClusterClientTest`)
   - Tests for all client operations (set, get, delete, etc.)
   - Tests for batch operations
   - Tests for error handling

5. **Batch Operation Tests** (`BatchOperationBuilderTest`)
   - Tests for building batch operations
   - Tests for all operation types
   - Tests for method chaining

6. **Authentication Tests** (`AuthenticationManagerTest`)
   - Tests for authentication with valid credentials
   - Tests for authentication failure scenarios
   - Tests for session token management
   - Tests for authentication state tracking

### Test Coverage

The test suite covers:
- ✅ Configuration validation and node assignment
- ✅ Connection management and failover
- ✅ All client API methods
- ✅ Batch operation building
- ✅ Authentication and session management
- ✅ Error handling and edge cases
- ✅ Java 17 features (records, pattern matching, etc.)

### Mock Testing

Tests use Mockito for mocking gRPC stubs and connection pools, allowing for:
- Isolated unit testing
- Simulation of network failures
- Testing of retry and failover logic
- Verification of method calls and parameters

## Building from Source

1. Clone the repository
2. Generate gRPC classes and build with Maven:

```bash
# Generate gRPC classes from protobuf
mvn protobuf:compile protobuf:compile-custom

# Build the project
mvn clean package
```

3. Run tests:

```bash
mvn test
```

### Note on gRPC Code Generation

The project uses protobuf to generate gRPC client classes. The generated classes will be placed in:
- `target/generated-sources/protobuf/java/` - Protocol buffer message classes
- `target/generated-sources/protobuf/grpc-java/` - gRPC service stubs

If you encounter compilation issues related to `javax.annotation.Generated`, ensure that both `javax.annotation-api` and `jakarta.annotation-api` dependencies are included in your classpath (both are included in the pom.xml for compatibility).

## License

This project is licensed under the MIT License - see the LICENSE file for details.
