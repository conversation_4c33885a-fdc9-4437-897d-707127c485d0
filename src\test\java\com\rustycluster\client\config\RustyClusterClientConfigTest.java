package com.rustycluster.client.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class RustyClusterClientConfigTest {

    @Test
    @DisplayName("Should create a config with default values")
    void shouldCreateConfigWithDefaultValues() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .build();

        // Then - Updated for high-throughput optimized defaults
        assertThat(config.getNodes()).hasSize(1);
        assertThat(config.getMaxConnectionsPerNode()).isEqualTo(20); // Increased from 10
        assertThat(config.getConnectionTimeoutMs()).isEqualTo(3000); // Reduced from 5000
        assertThat(config.getReadTimeoutMs()).isEqualTo(2000); // Reduced from 5000
        assertThat(config.getWriteTimeoutMs()).isEqualTo(2000); // Reduced from 5000
        assertThat(config.getMaxRetries()).isEqualTo(2); // Reduced from 3
        assertThat(config.getRetryDelayMs()).isEqualTo(100); // Reduced from 500
        assertThat(config.isUseSecureConnection()).isFalse();
        assertThat(config.getTlsCertPath()).isNull();
    }

    @Test
    @DisplayName("Should create a config with custom values")
    void shouldCreateConfigWithCustomValues() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .maxConnectionsPerNode(20)
                .connectionTimeout(10, TimeUnit.SECONDS)
                .readTimeout(8, TimeUnit.SECONDS)
                .writeTimeout(8, TimeUnit.SECONDS)
                .maxRetries(5)
                .retryDelay(1000, TimeUnit.MILLISECONDS)
                .useSecureConnection("/path/to/cert.pem")
                .build();

        // Then
        assertThat(config.getNodes()).hasSize(1);
        assertThat(config.getMaxConnectionsPerNode()).isEqualTo(20);
        assertThat(config.getConnectionTimeoutMs()).isEqualTo(10000);
        assertThat(config.getReadTimeoutMs()).isEqualTo(8000);
        assertThat(config.getWriteTimeoutMs()).isEqualTo(8000);
        assertThat(config.getMaxRetries()).isEqualTo(5);
        assertThat(config.getRetryDelayMs()).isEqualTo(1000);
        assertThat(config.isUseSecureConnection()).isTrue();
        assertThat(config.getTlsCertPath()).isEqualTo("/path/to/cert.pem");
    }

    @Test
    @DisplayName("Should throw exception when no nodes are configured")
    void shouldThrowExceptionWhenNoNodesAreConfigured() {
        assertThatThrownBy(() -> RustyClusterClientConfig.builder().build())
                .isInstanceOf(IllegalStateException.class)
                .hasMessage("At least one node must be configured");
    }

    @Test
    @DisplayName("Should add a primary node")
    void shouldAddPrimaryNode() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .build();

        // Then
        assertThat(config.getNodes()).hasSize(1);
        NodeConfig node = config.getNodes().get(0);
        assertThat(node.host()).isEqualTo("localhost");
        assertThat(node.port()).isEqualTo(50051);
        assertThat(node.role()).isEqualTo(NodeRole.PRIMARY);
    }

    @Test
    @DisplayName("Should add a secondary node")
    void shouldAddSecondaryNode() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addSecondaryNode("localhost", 50052)
                .build();

        // Then
        assertThat(config.getNodes()).hasSize(1);
        NodeConfig node = config.getNodes().get(0);
        assertThat(node.host()).isEqualTo("localhost");
        assertThat(node.port()).isEqualTo(50052);
        assertThat(node.role()).isEqualTo(NodeRole.SECONDARY);
    }

    @Test
    @DisplayName("Should add a tertiary node")
    void shouldAddTertiaryNode() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addTertiaryNode("localhost", 50053)
                .build();

        // Then
        assertThat(config.getNodes()).hasSize(1);
        NodeConfig node = config.getNodes().get(0);
        assertThat(node.host()).isEqualTo("localhost");
        assertThat(node.port()).isEqualTo(50053);
        assertThat(node.role()).isEqualTo(NodeRole.TERTIARY);
    }

    @Test
    @DisplayName("Should add multiple primary nodes")
    void shouldAddMultiplePrimaryNodes() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNodes("localhost:50051", "localhost:50052")
                .build();

        // Then
        assertThat(config.getNodes()).hasSize(2);
        assertThat(config.getNodes().get(0).role()).isEqualTo(NodeRole.PRIMARY);
        assertThat(config.getNodes().get(1).role()).isEqualTo(NodeRole.PRIMARY);
    }

    @Test
    @DisplayName("Should add multiple secondary nodes")
    void shouldAddMultipleSecondaryNodes() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addSecondaryNodes("localhost:50051", "localhost:50052")
                .build();

        // Then
        assertThat(config.getNodes()).hasSize(2);
        assertThat(config.getNodes().get(0).role()).isEqualTo(NodeRole.SECONDARY);
        assertThat(config.getNodes().get(1).role()).isEqualTo(NodeRole.SECONDARY);
    }

    @Test
    @DisplayName("Should add multiple tertiary nodes")
    void shouldAddMultipleTertiaryNodes() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addTertiaryNodes("localhost:50051", "localhost:50052")
                .build();

        // Then
        assertThat(config.getNodes()).hasSize(2);
        assertThat(config.getNodes().get(0).role()).isEqualTo(NodeRole.TERTIARY);
        assertThat(config.getNodes().get(1).role()).isEqualTo(NodeRole.TERTIARY);
    }

    @Test
    @DisplayName("Should add a node with a custom role")
    void shouldAddNodeWithCustomRole() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNode("localhost", 50051, NodeRole.PRIMARY)
                .build();

        // Then
        assertThat(config.getNodes()).hasSize(1);
        NodeConfig node = config.getNodes().get(0);
        assertThat(node.host()).isEqualTo("localhost");
        assertThat(node.port()).isEqualTo(50051);
        assertThat(node.role()).isEqualTo(NodeRole.PRIMARY);
    }

    @Test
    @DisplayName("Should add multiple nodes with the same role")
    void shouldAddMultipleNodesWithSameRole() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes(NodeRole.PRIMARY, "localhost:50051", "localhost:50052")
                .build();

        // Then
        assertThat(config.getNodes()).hasSize(2);
        assertThat(config.getNodes().get(0).role()).isEqualTo(NodeRole.PRIMARY);
        assertThat(config.getNodes().get(1).role()).isEqualTo(NodeRole.PRIMARY);
    }

    @Test
    @DisplayName("Should throw exception for invalid host:port format")
    void shouldThrowExceptionForInvalidHostPortFormat() {
        assertThatThrownBy(() -> RustyClusterClientConfig.builder()
                .addNodes(NodeRole.PRIMARY, "localhost")
                .build())
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Invalid host:port format");
    }

    @Test
    @DisplayName("Should throw exception for invalid port number")
    void shouldThrowExceptionForInvalidPortNumber() {
        assertThatThrownBy(() -> RustyClusterClientConfig.builder()
                .addNodes(NodeRole.PRIMARY, "localhost:abc")
                .build())
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Invalid port number");
    }

    @Test
    @DisplayName("Should automatically assign roles based on order - single node")
    void shouldAutomaticallyAssignRolesBasedOnOrderSingleNode() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051")
                .build();

        // Then
        List<NodeConfig> nodes = config.getNodes();
        assertThat(nodes).hasSize(1);
        assertThat(nodes.get(0).role()).isEqualTo(NodeRole.PRIMARY);
    }

    @Test
    @DisplayName("Should automatically assign roles based on order - two nodes")
    void shouldAutomaticallyAssignRolesBasedOnOrderTwoNodes() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051", "localhost:50052")
                .build();

        // Then
        List<NodeConfig> nodes = config.getNodes();
        assertThat(nodes).hasSize(2);
        assertThat(nodes.get(0).role()).isEqualTo(NodeRole.PRIMARY);
        assertThat(nodes.get(1).role()).isEqualTo(NodeRole.SECONDARY);
    }

    @Test
    @DisplayName("Should automatically assign roles based on order - three nodes")
    void shouldAutomaticallyAssignRolesBasedOnOrderThreeNodes() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051", "localhost:50052", "localhost:50053")
                .build();

        // Then
        List<NodeConfig> nodes = config.getNodes();
        assertThat(nodes).hasSize(3);
        assertThat(nodes.get(0).role()).isEqualTo(NodeRole.PRIMARY);
        assertThat(nodes.get(1).role()).isEqualTo(NodeRole.SECONDARY);
        assertThat(nodes.get(2).role()).isEqualTo(NodeRole.TERTIARY);
    }

    @Test
    @DisplayName("Should automatically assign roles based on order - four nodes")
    void shouldAutomaticallyAssignRolesBasedOnOrderFourNodes() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051", "localhost:50052", "localhost:50053", "localhost:50054")
                .build();

        // Then
        List<NodeConfig> nodes = config.getNodes();
        assertThat(nodes).hasSize(4);
        assertThat(nodes.get(0).role()).isEqualTo(NodeRole.PRIMARY);
        assertThat(nodes.get(1).role()).isEqualTo(NodeRole.SECONDARY);
        assertThat(nodes.get(2).role()).isEqualTo(NodeRole.TERTIARY);
        assertThat(nodes.get(3).role()).isEqualTo(NodeRole.TERTIARY);
    }

    @Test
    @DisplayName("Should consider existing nodes when assigning roles automatically")
    void shouldConsiderExistingNodesWhenAssigningRolesAutomatically() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .addNodes("localhost:50052", "localhost:50053")
                .build();

        // Then
        List<NodeConfig> nodes = config.getNodes();
        assertThat(nodes).hasSize(3);
        assertThat(nodes.get(0).role()).isEqualTo(NodeRole.PRIMARY);
        assertThat(nodes.get(1).role()).isEqualTo(NodeRole.SECONDARY);
        assertThat(nodes.get(2).role()).isEqualTo(NodeRole.TERTIARY);
    }

    @Test
    @DisplayName("Should mix automatic and explicit role assignment")
    void shouldMixAutomaticAndExplicitRoleAssignment() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051")  // PRIMARY
                .addSecondaryNode("localhost", 50052)
                .addNodes("localhost:50053")  // TERTIARY
                .build();

        // Then
        List<NodeConfig> nodes = config.getNodes();
        assertThat(nodes).hasSize(3);
        assertThat(nodes.get(0).role()).isEqualTo(NodeRole.PRIMARY);
        assertThat(nodes.get(1).role()).isEqualTo(NodeRole.SECONDARY);
        assertThat(nodes.get(2).role()).isEqualTo(NodeRole.TERTIARY);
    }

    @Test
    @DisplayName("Should apply high-throughput preset correctly")
    void shouldApplyHighThroughputPreset() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .highThroughputPreset()
                .build();

        // Then
        assertThat(config.getMaxConnectionsPerNode()).isEqualTo(50);
        assertThat(config.getConnectionTimeoutMs()).isEqualTo(1000);
        assertThat(config.getReadTimeoutMs()).isEqualTo(1000);
        assertThat(config.getWriteTimeoutMs()).isEqualTo(1000);
        assertThat(config.getMaxRetries()).isEqualTo(1);
        assertThat(config.getRetryDelayMs()).isEqualTo(50);
    }

    @Test
    @DisplayName("Should apply low-latency preset correctly")
    void shouldApplyLowLatencyPreset() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .lowLatencyPreset()
                .build();

        // Then
        assertThat(config.getMaxConnectionsPerNode()).isEqualTo(10);
        assertThat(config.getConnectionTimeoutMs()).isEqualTo(500);
        assertThat(config.getReadTimeoutMs()).isEqualTo(500);
        assertThat(config.getWriteTimeoutMs()).isEqualTo(500);
        assertThat(config.getMaxRetries()).isEqualTo(0);
        assertThat(config.getRetryDelayMs()).isEqualTo(0);
    }

    @Test
    @DisplayName("Should apply balanced preset correctly")
    void shouldApplyBalancedPreset() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .balancedPreset()
                .build();

        // Then
        assertThat(config.getMaxConnectionsPerNode()).isEqualTo(20);
        assertThat(config.getConnectionTimeoutMs()).isEqualTo(3000);
        assertThat(config.getReadTimeoutMs()).isEqualTo(2000);
        assertThat(config.getWriteTimeoutMs()).isEqualTo(2000);
        assertThat(config.getMaxRetries()).isEqualTo(2);
        assertThat(config.getRetryDelayMs()).isEqualTo(100);
    }
}
