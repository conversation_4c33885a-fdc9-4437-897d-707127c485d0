package com.rustycluster.client.connection;

import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import com.rustycluster.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages automatic failback to higher-priority nodes when they become available again.
 */
public class FailbackManager implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(FailbackManager.class);

    private final RustyClusterClientConfig config;
    private final ConnectionPool connectionPool;
    private final List<NodeConfig> sortedNodes;
    private final AtomicReference<NodeConfig> currentNode;
    private final ScheduledExecutorService scheduler;
    private volatile boolean running = false;

    /**
     * Create a new FailbackManager.
     *
     * @param config         The client configuration
     * @param connectionPool The connection pool
     * @param sortedNodes    The list of nodes sorted by priority
     * @param currentNode    The current active node reference
     */
    public FailbackManager(RustyClusterClientConfig config, 
                          ConnectionPool connectionPool,
                          List<NodeConfig> sortedNodes,
                          AtomicReference<NodeConfig> currentNode) {
        this.config = config;
        this.connectionPool = connectionPool;
        this.sortedNodes = sortedNodes;
        this.currentNode = currentNode;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "FailbackManager");
            t.setDaemon(true);
            return t;
        });
    }

    /**
     * Start the failback manager.
     */
    public void start() {
        if (!config.isEnableFailback()) {
            logger.debug("Failback is disabled, not starting FailbackManager");
            return;
        }

        if (running) {
            logger.warn("FailbackManager is already running");
            return;
        }

        running = true;
        scheduler.scheduleWithFixedDelay(
            this::checkForFailback,
            config.getFailbackCheckIntervalMs(),
            config.getFailbackCheckIntervalMs(),
            TimeUnit.MILLISECONDS
        );
        
        logger.info("FailbackManager started with check interval: {}ms", config.getFailbackCheckIntervalMs());
    }

    /**
     * Stop the failback manager.
     */
    public void stop() {
        running = false;
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("FailbackManager stopped");
    }

    /**
     * Check if we can failback to a higher-priority node.
     */
    private void checkForFailback() {
        if (!running) {
            return;
        }

        try {
            NodeConfig current = currentNode.get();
            if (current == null) {
                return;
            }

            // Find the highest priority node that's available
            NodeConfig bestAvailableNode = findBestAvailableNode();
            
            if (bestAvailableNode != null && 
                bestAvailableNode.role().getPriority() < current.role().getPriority()) {
                
                logger.info("Failing back from {} (priority {}) to {} (priority {})",
                    current, current.role().getPriority(),
                    bestAvailableNode, bestAvailableNode.role().getPriority());
                
                currentNode.set(bestAvailableNode);
            }
        } catch (Exception e) {
            logger.warn("Error during failback check: {}", e.getMessage());
        }
    }

    /**
     * Find the best available node (highest priority that's healthy).
     * Only checks nodes with higher priority than the current node.
     *
     * @return The best available node, or null if none are available
     */
    private NodeConfig findBestAvailableNode() {
        NodeConfig current = currentNode.get();
        if (current == null) {
            return null;
        }

        // Only check nodes with higher priority (lower priority number) than current
        for (NodeConfig node : sortedNodes) {
            if (node.role().getPriority() < current.role().getPriority()) {
                if (isNodeHealthy(node)) {
                    return node;
                }
            } else {
                // Since nodes are sorted by priority, we can break early
                // when we reach nodes with same or lower priority
                break;
            }
        }
        return null;
    }

    /**
     * Check if a node is healthy by performing multiple health checks.
     *
     * @param node The node to check
     * @return True if the node is healthy, false otherwise
     */
    private boolean isNodeHealthy(NodeConfig node) {
        int successfulChecks = 0;
        int requiredChecks = config.getFailbackHealthCheckRetries();

        for (int i = 0; i < requiredChecks; i++) {
            if (performHealthCheck(node)) {
                successfulChecks++;
            } else {
                // If any check fails, consider the node unhealthy
                break;
            }
            
            // Small delay between checks to avoid overwhelming the node
            if (i < requiredChecks - 1) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }

        boolean isHealthy = successfulChecks == requiredChecks;
        if (isHealthy) {
            logger.debug("Node {} passed {}/{} health checks", node, successfulChecks, requiredChecks);
        } else {
            logger.debug("Node {} failed health check ({}/{} successful)", node, successfulChecks, requiredChecks);
        }
        
        return isHealthy;
    }

    /**
     * Perform a single health check on a node.
     *
     * @param node The node to check
     * @return True if the health check passed, false otherwise
     */
    private boolean performHealthCheck(NodeConfig node) {
        KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = null;
        try {
            stub = connectionPool.borrowStub(node);
            
            // Apply a short timeout for health checks
            KeyValueServiceGrpc.KeyValueServiceBlockingStub stubWithDeadline = 
                stub.withDeadlineAfter(1000, TimeUnit.MILLISECONDS);
            
            // Perform a simple ping operation (get a non-existent key)
            RustyClusterProto.GetRequest healthCheckRequest = RustyClusterProto.GetRequest.newBuilder()
                .setKey("__health_check__")
                .build();
            
            stubWithDeadline.get(healthCheckRequest);
            return true;
            
        } catch (Exception e) {
            logger.debug("Health check failed for node {}: {}", node, e.getMessage());
            return false;
        } finally {
            if (stub != null) {
                connectionPool.returnStub(node, stub);
            }
        }
    }

    @Override
    public void close() {
        stop();
    }
}
