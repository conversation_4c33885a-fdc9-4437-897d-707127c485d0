<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PerformanceMetrics</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">com.rustycluster.client.metrics</a> &gt; <span class="el_class">PerformanceMetrics</span></div><h1>PerformanceMetrics</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">422 of 422</td><td class="ctr2">0%</td><td class="bar">8 of 8</td><td class="ctr2">0%</td><td class="ctr1">25</td><td class="ctr2">25</td><td class="ctr1">87</td><td class="ctr2">87</td><td class="ctr1">21</td><td class="ctr2">21</td></tr></tfoot><tbody><tr><td id="a1"><a href="PerformanceMetrics.java.html#L125" class="el_method">getStats()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="115" alt="115"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h1">17</td><td class="ctr2" id="i1">17</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a10"><a href="PerformanceMetrics.java.html#L22" class="el_method">PerformanceMetrics()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="82" alt="82"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">1</td><td class="ctr1" id="h0">18</td><td class="ctr2" id="i0">18</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a19"><a href="PerformanceMetrics.java.html#L154" class="el_method">reset()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="54" height="10" title="52" alt="52"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">17</td><td class="ctr2" id="i2">17</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a17"><a href="PerformanceMetrics.java.html#L61" class="el_method">recordOperation(String, Duration)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="43" alt="43"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">9</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a9"><a href="PerformanceMetrics.java.html#L176" class="el_method">logStats()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="33" alt="33"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">4</td><td class="ctr2" id="i4">4</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a16"><a href="PerformanceMetrics.java.html#L116" class="el_method">recordFailover(String, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="16" alt="16"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">4</td><td class="ctr2" id="i5">4</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a15"><a href="PerformanceMetrics.java.html#L80" class="el_method">recordFailedOperation(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="15" alt="15"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">4</td><td class="ctr2" id="i6">4</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a18"><a href="PerformanceMetrics.java.html#L89" class="el_method">recordRetry(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="12" alt="12"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a20"><a href="PerformanceMetrics.java.html#L17" class="el_method">static {...}</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="8" alt="8"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a11"><a href="PerformanceMetrics.java.html#L97" class="el_method">recordConnectionPoolBorrow()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a14"><a href="PerformanceMetrics.java.html#L101" class="el_method">recordConnectionPoolReturn()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">2</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a12"><a href="PerformanceMetrics.java.html#L105" class="el_method">recordConnectionPoolCreation()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a13"><a href="PerformanceMetrics.java.html#L109" class="el_method">recordConnectionPoolDestruction()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a3"><a href="PerformanceMetrics.java.html#L117" class="el_method">lambda$recordFailover$6(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a8"><a href="PerformanceMetrics.java.html#L90" class="el_method">lambda$recordRetry$5(String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a2"><a href="PerformanceMetrics.java.html#L82" class="el_method">lambda$recordFailedOperation$4(String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a7"><a href="PerformanceMetrics.java.html#L73" class="el_method">lambda$recordOperation$3(String)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a6"><a href="PerformanceMetrics.java.html#L72" class="el_method">lambda$recordOperation$2(String)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a5"><a href="PerformanceMetrics.java.html#L69" class="el_method">lambda$recordOperation$1(long, long)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a4"><a href="PerformanceMetrics.java.html#L68" class="el_method">lambda$recordOperation$0(long, long)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a0"><a href="PerformanceMetrics.java.html#L54" class="el_method">getInstance()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>