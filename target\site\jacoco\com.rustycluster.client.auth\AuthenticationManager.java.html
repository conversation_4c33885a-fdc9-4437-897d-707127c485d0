<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AuthenticationManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.auth</a> &gt; <span class="el_source">AuthenticationManager.java</span></div><h1>AuthenticationManager.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.auth;

import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import com.rustycluster.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages authentication for RustyCluster client connections.
 */
public class AuthenticationManager {
<span class="fc" id="L15">    private static final Logger logger = LoggerFactory.getLogger(AuthenticationManager.class);</span>

    private final RustyClusterClientConfig config;
<span class="fc" id="L18">    private final AtomicReference&lt;String&gt; sessionToken = new AtomicReference&lt;&gt;();</span>
<span class="fc" id="L19">    private volatile boolean isAuthenticated = false;</span>

    /**
     * Create a new AuthenticationManager.
     *
     * @param config The client configuration
     */
<span class="fc" id="L26">    public AuthenticationManager(RustyClusterClientConfig config) {</span>
<span class="fc" id="L27">        this.config = config;</span>
<span class="fc" id="L28">    }</span>

    /**
     * Authenticate with the RustyCluster server.
     *
     * @param stub The gRPC stub to use for authentication
     * @return True if authentication was successful, false otherwise
     */
    public boolean authenticate(KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) {
<span class="fc bfc" id="L37" title="All 2 branches covered.">        if (!config.hasAuthentication()) {</span>
<span class="fc" id="L38">            logger.debug(&quot;No authentication credentials configured, skipping authentication&quot;);</span>
<span class="fc" id="L39">            return true;</span>
        }

        try {
<span class="fc" id="L43">            logger.debug(&quot;Authenticating with username: {}&quot;, config.getUsername());</span>

            // Create authentication request
<span class="fc" id="L46">            RustyClusterProto.AuthenticateRequest request = RustyClusterProto.AuthenticateRequest.newBuilder()</span>
<span class="fc" id="L47">                    .setUsername(config.getUsername())</span>
<span class="fc" id="L48">                    .setPassword(config.getPassword())</span>
<span class="fc" id="L49">                    .build();</span>

            // Make authentication call to server
<span class="fc" id="L52">            RustyClusterProto.AuthenticateResponse response = stub.authenticate(request);</span>

<span class="pc bpc" id="L54" title="1 of 2 branches missed.">            if (response.getSuccess()) {</span>
<span class="fc" id="L55">                sessionToken.set(response.getSessionToken());</span>
<span class="fc" id="L56">                isAuthenticated = true;</span>
<span class="fc" id="L57">                logger.info(&quot;Authentication successful for user: {}&quot;, config.getUsername());</span>
<span class="fc" id="L58">                return true;</span>
            } else {
<span class="nc" id="L60">                logger.warn(&quot;Authentication failed for user: {} - {}&quot;, config.getUsername(), response.getMessage());</span>
<span class="nc" id="L61">                isAuthenticated = false;</span>
<span class="nc" id="L62">                sessionToken.set(null);</span>
<span class="nc" id="L63">                return false;</span>
            }

<span class="nc" id="L66">        } catch (Exception e) {</span>
<span class="nc" id="L67">            logger.error(&quot;Authentication failed for user: {}&quot;, config.getUsername(), e);</span>
<span class="nc" id="L68">            isAuthenticated = false;</span>
<span class="nc" id="L69">            sessionToken.set(null);</span>
<span class="nc" id="L70">            return false;</span>
        }
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
<span class="fc" id="L80">        return isAuthenticated;</span>
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
<span class="fc" id="L89">        return sessionToken.get();</span>
    }

    /**
     * Clear the authentication state.
     */
    public void clearAuthentication() {
<span class="fc" id="L96">        isAuthenticated = false;</span>
<span class="fc" id="L97">        sessionToken.set(null);</span>
<span class="fc" id="L98">        logger.debug(&quot;Authentication state cleared&quot;);</span>
<span class="fc" id="L99">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>