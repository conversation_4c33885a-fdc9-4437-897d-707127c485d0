package com.rustycluster.client.connection;

import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import com.rustycluster.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages automatic failback to higher-priority nodes for async operations.
 */
public class AsyncFailbackManager implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(AsyncFailbackManager.class);

    private final RustyClusterClientConfig config;
    private final AsyncConnectionPool connectionPool;
    private final List<NodeConfig> sortedNodes;
    private final AtomicReference<NodeConfig> currentNode;
    private final ScheduledExecutorService scheduler;
    private volatile boolean running = false;

    /**
     * Create a new AsyncFailbackManager.
     *
     * @param config         The client configuration
     * @param connectionPool The async connection pool
     * @param sortedNodes    The list of nodes sorted by priority
     * @param currentNode    The current active node reference
     */
    public AsyncFailbackManager(RustyClusterClientConfig config, 
                               AsyncConnectionPool connectionPool,
                               List<NodeConfig> sortedNodes,
                               AtomicReference<NodeConfig> currentNode) {
        this.config = config;
        this.connectionPool = connectionPool;
        this.sortedNodes = sortedNodes;
        this.currentNode = currentNode;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "AsyncFailbackManager");
            t.setDaemon(true);
            return t;
        });
    }

    /**
     * Start the failback manager.
     */
    public void start() {
        if (!config.isEnableFailback()) {
            logger.debug("Failback is disabled, not starting AsyncFailbackManager");
            return;
        }

        if (running) {
            logger.warn("AsyncFailbackManager is already running");
            return;
        }

        running = true;
        scheduler.scheduleWithFixedDelay(
            this::checkForFailback,
            config.getFailbackCheckIntervalMs(),
            config.getFailbackCheckIntervalMs(),
            TimeUnit.MILLISECONDS
        );
        
        logger.info("AsyncFailbackManager started with check interval: {}ms", config.getFailbackCheckIntervalMs());
    }

    /**
     * Stop the failback manager.
     */
    public void stop() {
        running = false;
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("AsyncFailbackManager stopped");
    }

    /**
     * Check if we can failback to a higher-priority node.
     */
    private void checkForFailback() {
        if (!running) {
            return;
        }

        try {
            NodeConfig current = currentNode.get();
            if (current == null) {
                return;
            }

            // Find the highest priority node that's available
            findBestAvailableNodeAsync()
                .thenAccept(bestAvailableNode -> {
                    if (bestAvailableNode != null && 
                        bestAvailableNode.role().getPriority() < current.role().getPriority()) {
                        
                        logger.info("Failing back from {} (priority {}) to {} (priority {})",
                            current, current.role().getPriority(),
                            bestAvailableNode, bestAvailableNode.role().getPriority());
                        
                        currentNode.set(bestAvailableNode);
                    }
                })
                .exceptionally(e -> {
                    logger.warn("Error during async failback check: {}", e.getMessage());
                    return null;
                });
        } catch (Exception e) {
            logger.warn("Error during failback check: {}", e.getMessage());
        }
    }

    /**
     * Find the best available node (highest priority that's healthy) asynchronously.
     *
     * @return CompletableFuture with the best available node, or null if none are available
     */
    private CompletableFuture<NodeConfig> findBestAvailableNodeAsync() {
        return checkNodesSequentially(0);
    }

    /**
     * Check nodes sequentially starting from the given index.
     *
     * @param nodeIndex The index of the node to check
     * @return CompletableFuture with the first healthy node found, or null
     */
    private CompletableFuture<NodeConfig> checkNodesSequentially(int nodeIndex) {
        if (nodeIndex >= sortedNodes.size()) {
            return CompletableFuture.completedFuture(null);
        }

        NodeConfig node = sortedNodes.get(nodeIndex);
        return isNodeHealthyAsync(node)
            .thenCompose(isHealthy -> {
                if (isHealthy) {
                    return CompletableFuture.completedFuture(node);
                } else {
                    return checkNodesSequentially(nodeIndex + 1);
                }
            });
    }

    /**
     * Check if a node is healthy asynchronously.
     *
     * @param node The node to check
     * @return CompletableFuture with true if the node is healthy, false otherwise
     */
    private CompletableFuture<Boolean> isNodeHealthyAsync(NodeConfig node) {
        return performHealthCheckAsync(node, 0);
    }

    /**
     * Perform health checks recursively with retries.
     *
     * @param node The node to check
     * @param attempt The current attempt number
     * @return CompletableFuture with true if all health checks pass, false otherwise
     */
    private CompletableFuture<Boolean> performHealthCheckAsync(NodeConfig node, int attempt) {
        if (attempt >= config.getFailbackHealthCheckRetries()) {
            return CompletableFuture.completedFuture(true);
        }

        return connectionPool.borrowStubAsync(node)
            .thenCompose(stub -> {
                try {
                    // Apply a short timeout for health checks
                    KeyValueServiceGrpc.KeyValueServiceFutureStub stubWithDeadline = 
                        stub.withDeadlineAfter(1000, TimeUnit.MILLISECONDS);
                    
                    // Perform a simple ping operation (get a non-existent key)
                    RustyClusterProto.GetRequest healthCheckRequest = RustyClusterProto.GetRequest.newBuilder()
                        .setKey("__health_check__")
                        .build();
                    
                    return toCompletableFuture(stubWithDeadline.get(healthCheckRequest))
                        .thenApply(response -> true)
                        .exceptionally(e -> {
                            logger.debug("Health check failed for node {} (attempt {}): {}", 
                                node, attempt + 1, e.getMessage());
                            return false;
                        })
                        .whenComplete((result, throwable) -> {
                            connectionPool.returnStub(node, stub);
                        });
                } catch (Exception e) {
                    connectionPool.returnStub(node, stub);
                    return CompletableFuture.completedFuture(false);
                }
            })
            .thenCompose(success -> {
                if (success && attempt < config.getFailbackHealthCheckRetries() - 1) {
                    // Add a small delay between checks
                    CompletableFuture<Void> delay = new CompletableFuture<>();
                    CompletableFuture.delayedExecutor(100, TimeUnit.MILLISECONDS)
                        .execute(() -> delay.complete(null));
                    return delay.thenCompose(v -> performHealthCheckAsync(node, attempt + 1));
                } else {
                    return CompletableFuture.completedFuture(success);
                }
            })
            .exceptionally(e -> {
                logger.debug("Health check failed for node {} (attempt {}): {}", 
                    node, attempt + 1, e.getMessage());
                return false;
            });
    }

    /**
     * Convert a ListenableFuture to CompletableFuture.
     */
    private <T> CompletableFuture<T> toCompletableFuture(com.google.common.util.concurrent.ListenableFuture<T> listenableFuture) {
        CompletableFuture<T> completableFuture = new CompletableFuture<>();
        com.google.common.util.concurrent.Futures.addCallback(
            listenableFuture,
            new com.google.common.util.concurrent.FutureCallback<T>() {
                @Override
                public void onSuccess(T result) {
                    completableFuture.complete(result);
                }

                @Override
                public void onFailure(Throwable t) {
                    completableFuture.completeExceptionally(t);
                }
            },
            Runnable::run
        );
        return completableFuture;
    }

    @Override
    public void close() {
        stop();
    }
}
