<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AuthenticationInterceptor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.interceptor</a> &gt; <span class="el_source">AuthenticationInterceptor.java</span></div><h1>AuthenticationInterceptor.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.interceptor;

import com.rustycluster.client.auth.AuthenticationManager;
import io.grpc.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * gRPC client interceptor that automatically adds authentication headers to requests.
 */
public class AuthenticationInterceptor implements ClientInterceptor {
    
<span class="nc" id="L13">    private static final Logger logger = LoggerFactory.getLogger(AuthenticationInterceptor.class);</span>
<span class="nc" id="L14">    private static final Metadata.Key&lt;String&gt; AUTHORIZATION_HEADER = </span>
<span class="nc" id="L15">            Metadata.Key.of(&quot;authorization&quot;, Metadata.ASCII_STRING_MARSHALLER);</span>
    
    private final AuthenticationManager authenticationManager;
    
<span class="nc" id="L19">    public AuthenticationInterceptor(AuthenticationManager authenticationManager) {</span>
<span class="nc" id="L20">        this.authenticationManager = authenticationManager;</span>
<span class="nc" id="L21">    }</span>
    
    @Override
    public &lt;ReqT, RespT&gt; ClientCall&lt;ReqT, RespT&gt; interceptCall(
            MethodDescriptor&lt;ReqT, RespT&gt; method,
            CallOptions callOptions,
            Channel next) {
        
<span class="nc" id="L29">        return new ForwardingClientCall.SimpleForwardingClientCall&lt;ReqT, RespT&gt;(</span>
<span class="nc" id="L30">                next.newCall(method, callOptions)) {</span>
            
            @Override
            public void start(Listener&lt;RespT&gt; responseListener, Metadata headers) {
                // Add authorization header if authenticated
<span class="nc bnc" id="L35" title="All 2 branches missed.">                if (authenticationManager.isAuthenticated()) {</span>
<span class="nc" id="L36">                    String sessionToken = authenticationManager.getSessionToken();</span>
<span class="nc bnc" id="L37" title="All 2 branches missed.">                    if (sessionToken != null) {</span>
<span class="nc" id="L38">                        headers.put(AUTHORIZATION_HEADER, &quot;Bearer &quot; + sessionToken);</span>
<span class="nc" id="L39">                        logger.debug(&quot;Added authorization header for method: {}&quot;, method.getFullMethodName());</span>
                    }
                }
                
<span class="nc" id="L43">                super.start(responseListener, headers);</span>
<span class="nc" id="L44">            }</span>
        };
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>