<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OperationType.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.connection</a> &gt; <span class="el_source">OperationType.java</span></div><h1>OperationType.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.connection;

/**
 * Enum representing different types of operations for timeout configuration.
 */
<span class="fc" id="L6">public enum OperationType {</span>
    /**
     * Read operations (get, hGet, hGetAll, etc.)
     * These operations should use readTimeoutMs.
     */
<span class="fc" id="L11">    READ,</span>
    
    /**
     * Write operations (set, setEx, hSet, delete, etc.)
     * These operations should use writeTimeoutMs.
     */
<span class="fc" id="L17">    WRITE,</span>
    
    /**
     * Authentication operations
     * These operations should use connectionTimeoutMs.
     */
<span class="fc" id="L23">    AUTH</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>