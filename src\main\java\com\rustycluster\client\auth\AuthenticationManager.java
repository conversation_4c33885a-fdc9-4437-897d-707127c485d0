package com.rustycluster.client.auth;

import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import com.rustycluster.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages authentication for RustyC<PERSON> client connections.
 */
public class AuthenticationManager {
    private static final Logger logger = LoggerFactory.getLogger(AuthenticationManager.class);

    private final RustyClusterClientConfig config;
    private final AtomicReference<String> sessionToken = new AtomicReference<>();
    private volatile boolean isAuthenticated = false;

    /**
     * Create a new AuthenticationManager.
     *
     * @param config The client configuration
     */
    public AuthenticationManager(RustyClusterClientConfig config) {
        this.config = config;
    }

    /**
     * Authenticate with the RustyCluster server.
     *
     * @param stub The gRPC stub to use for authentication
     * @return True if authentication was successful, false otherwise
     */
    public boolean authenticate(KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) {
        if (!config.hasAuthentication()) {
            logger.debug("No authentication credentials configured, skipping authentication");
            return true;
        }

        try {
            logger.debug("Authenticating with username: {}", config.getUsername());

            // Create authentication request
            RustyClusterProto.AuthenticateRequest request = RustyClusterProto.AuthenticateRequest.newBuilder()
                    .setUsername(config.getUsername())
                    .setPassword(config.getPassword())
                    .build();

            // Make authentication call to server
            RustyClusterProto.AuthenticateResponse response = stub.authenticate(request);

            if (response.getSuccess()) {
                sessionToken.set(response.getSessionToken());
                isAuthenticated = true;
                logger.info("Authentication successful for user: {}", config.getUsername());
                return true;
            } else {
                logger.warn("Authentication failed for user: {} - {}", config.getUsername(), response.getMessage());
                isAuthenticated = false;
                sessionToken.set(null);
                return false;
            }

        } catch (Exception e) {
            logger.error("Authentication failed for user: {}", config.getUsername(), e);
            isAuthenticated = false;
            sessionToken.set(null);
            return false;
        }
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
        return isAuthenticated;
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
        return sessionToken.get();
    }

    /**
     * Clear the authentication state.
     */
    public void clearAuthentication() {
        isAuthenticated = false;
        sessionToken.set(null);
        logger.debug("Authentication state cleared");
    }
}
