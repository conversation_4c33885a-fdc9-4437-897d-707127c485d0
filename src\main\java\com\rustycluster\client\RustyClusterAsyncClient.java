package com.rustycluster.client;

import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.connection.AsyncConnectionManager;
import com.rustycluster.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Asynchronous client for interacting with RustyCluster.
 * This client provides non-blocking operations for high-throughput scenarios.
 */
public class RustyClusterAsyncClient implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(RustyClusterAsyncClient.class);

    private final RustyClusterClientConfig config;
    private final AsyncConnectionManager connectionManager;
    private final AuthenticationManager authenticationManager;

    /**
     * Create a new RustyClusterAsyncClient with the provided configuration.
     *
     * @param config The client configuration
     */
    public RustyClusterAsyncClient(RustyClusterClientConfig config) {
        this.config = config;
        this.authenticationManager = new AuthenticationManager(config);
        this.connectionManager = new AsyncConnectionManager(config, authenticationManager);
        logger.info("RustyClusterAsyncClient initialized");
    }

    /**
     * Set a key-value pair asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> setAsync(String key, String value, boolean skipReplication) {
        logger.debug("Setting key asynchronously: {}", key);
        RustyClusterProto.SetRequest request = RustyClusterProto.SetRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        return connectionManager.executeWithFailoverAsync(stub ->
                stub.set(request))
                .thenApply(RustyClusterProto.SetResponse::getSuccess);
    }

    /**
     * Set a key-value pair asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> setAsync(String key, String value) {
        return setAsync(key, value, false);
    }

    /**
     * Get a value by key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with the value, or null if not found
     */
    public CompletableFuture<String> getAsync(String key) {
        logger.debug("Getting key asynchronously: {}", key);
        RustyClusterProto.GetRequest request = RustyClusterProto.GetRequest.newBuilder()
                .setKey(key)
                .build();

        return connectionManager.executeWithFailoverAsync(stub ->
                stub.get(request))
                .thenApply(response -> response.getFound() ? response.getValue() : null);
    }

    /**
     * Delete a key asynchronously.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> deleteAsync(String key, boolean skipReplication) {
        logger.debug("Deleting key asynchronously: {}", key);
        RustyClusterProto.DeleteRequest request = RustyClusterProto.DeleteRequest.newBuilder()
                .setKey(key)
                .setSkipReplication(skipReplication)
                .build();

        return connectionManager.executeWithFailoverAsync(stub ->
                stub.delete(request))
                .thenApply(RustyClusterProto.DeleteResponse::getSuccess);
    }

    /**
     * Delete a key asynchronously with default replication.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> deleteAsync(String key) {
        return deleteAsync(key, false);
    }

    /**
     * Execute a batch of operations asynchronously.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture<List<Boolean>> batchWriteAsync(List<RustyClusterProto.BatchOperation> operations, boolean skipReplication) {
        logger.debug("Executing batch write asynchronously with {} operations", operations.size());
        RustyClusterProto.BatchWriteRequest request = RustyClusterProto.BatchWriteRequest.newBuilder()
                .addAllOperations(operations)
                .setSkipReplication(skipReplication)
                .build();

        return connectionManager.executeWithFailoverAsync(stub ->
                stub.batchWrite(request))
                .thenApply(RustyClusterProto.BatchWriteResponse::getOperationResultsList);
    }

    /**
     * Execute a batch of operations asynchronously with default replication.
     *
     * @param operations The list of operations to execute
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture<List<Boolean>> batchWriteAsync(List<RustyClusterProto.BatchOperation> operations) {
        return batchWriteAsync(operations, false);
    }

    /**
     * Increment a numeric value asynchronously.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture<Long> incrByAsync(String key, long value, boolean skipReplication) {
        logger.debug("Incrementing key asynchronously: {} by {}", key, value);
        RustyClusterProto.IncrByRequest request = RustyClusterProto.IncrByRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        return connectionManager.executeWithFailoverAsync(stub ->
                stub.incrBy(request))
                .thenApply(RustyClusterProto.IncrByResponse::getNewValue);
    }

    /**
     * Increment a numeric value asynchronously with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture<Long> incrByAsync(String key, long value) {
        return incrByAsync(key, value, false);
    }

    /**
     * Get all fields from a hash asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with a map of field names to values
     */
    public CompletableFuture<Map<String, String>> hGetAllAsync(String key) {
        logger.debug("Getting all hash fields asynchronously for key: {}", key);
        RustyClusterProto.HGetAllRequest request = RustyClusterProto.HGetAllRequest.newBuilder()
                .setKey(key)
                .build();

        return connectionManager.executeWithFailoverAsync(stub ->
                stub.hGetAll(request))
                .thenApply(RustyClusterProto.HGetAllResponse::getFieldsMap);
    }

    /**
     * Authenticate with the RustyCluster server asynchronously.
     *
     * @return CompletableFuture that completes with true if authentication was successful, false otherwise
     */
    public CompletableFuture<Boolean> authenticateAsync() {
        logger.debug("Attempting to authenticate asynchronously with RustyCluster server");

        if (!config.hasAuthentication()) {
            logger.debug("No authentication credentials configured");
            return CompletableFuture.completedFuture(true);
        }

        // For async authentication, we need to use a different approach
        // since authenticate method expects a blocking stub
        return CompletableFuture.supplyAsync(() -> {
            try {
                // This is a simplified approach - in a real implementation,
                // you'd want to create an async version of authenticate
                return authenticationManager.isAuthenticated();
            } catch (Exception e) {
                logger.error("Authentication check failed", e);
                return false;
            }
        });
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
        return authenticationManager.isAuthenticated();
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
        return authenticationManager.getSessionToken();
    }

    /**
     * Close the client and release all resources.
     */
    @Override
    public void close() {
        authenticationManager.clearAuthentication();
        connectionManager.close();
        logger.info("RustyClusterAsyncClient closed");
    }
}
