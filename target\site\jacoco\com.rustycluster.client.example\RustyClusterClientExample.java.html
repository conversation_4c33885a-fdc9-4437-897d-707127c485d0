<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterClientExample.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.example</a> &gt; <span class="el_source">RustyClusterClientExample.java</span></div><h1>RustyClusterClientExample.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.example;

import com.rustycluster.client.BatchOperationBuilder;
import com.rustycluster.client.RustyClusterClient;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.RustyClusterProto;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Example usage of the RustyClusterClient.
 */
<span class="nc" id="L15">public class RustyClusterClientExample {</span>

    public static void main(String[] args) {

        // Use the third configuration for this example (3 nodes with automatic role assignment)
<span class="nc" id="L20">        RustyClusterClientConfig config = RustyClusterClientConfig.builder()</span>
<span class="nc" id="L21">                .addNodes(&quot;localhost:50051&quot;, &quot;localhost:50052&quot;, &quot;localhost:50053&quot;)</span>
<span class="nc" id="L22">                .maxConnectionsPerNode(5)</span>
<span class="nc" id="L23">                .connectionTimeout(5, TimeUnit.SECONDS)</span>
<span class="nc" id="L24">                .readTimeout(3, TimeUnit.SECONDS)</span>
<span class="nc" id="L25">                .writeTimeout(3, TimeUnit.SECONDS)</span>
<span class="nc" id="L26">                .maxRetries(3)</span>
<span class="nc" id="L27">                .retryDelay(500, TimeUnit.MILLISECONDS)</span>
<span class="nc" id="L28">                .authentication(&quot;testuser&quot;, &quot;testpass&quot;)  // Add authentication credentials</span>
<span class="nc" id="L29">                .build();</span>

        // Create client
<span class="nc" id="L32">        try (RustyClusterClient client = new RustyClusterClient(config)) {</span>
            // Authenticate with the server
<span class="nc" id="L34">            System.out.println(&quot;Authenticating with RustyCluster server...&quot;);</span>
<span class="nc" id="L35">            boolean authResult = client.authenticate();</span>
<span class="nc" id="L36">            System.out.println(&quot;Authentication result: &quot; + authResult);</span>

<span class="nc bnc" id="L38" title="All 2 branches missed.">            if (!authResult) {</span>
<span class="nc" id="L39">                System.err.println(&quot;Authentication failed! Exiting...&quot;);</span>
<span class="nc" id="L40">                return;</span>
            }

<span class="nc" id="L43">            System.out.println(&quot;Authentication successful! Session token: &quot; + client.getSessionToken());</span>
 
            // String operations
<span class="nc" id="L46">            System.out.println(&quot;Setting key 'greeting' to 'Hello, RustyCluster!'&quot;);</span>
<span class="nc" id="L47">            boolean setResult = client.set(&quot;greeting&quot;, &quot;Hello, RustyCluster!&quot;);</span>
<span class="nc" id="L48">            System.out.println(&quot;Set result: &quot; + setResult);</span>

<span class="nc" id="L50">            System.out.println(&quot;Getting key 'greeting'&quot;);</span>
<span class="nc" id="L51">            String value = client.get(&quot;greeting&quot;);</span>
<span class="nc" id="L52">            System.out.println(&quot;Value: &quot; + value);</span>

<span class="nc" id="L54">            System.out.println(&quot;Setting key 'temp' with expiry&quot;);</span>
<span class="nc" id="L55">            boolean setExResult = client.setEx(&quot;temp&quot;, &quot;This will expire&quot;, 60);</span>
<span class="nc" id="L56">            System.out.println(&quot;SetEx result: &quot; + setExResult);</span>

            // Numeric operations
<span class="nc" id="L59">            System.out.println(&quot;Incrementing key 'counter'&quot;);</span>
<span class="nc" id="L60">            long counterValue = client.incrBy(&quot;counter&quot;, 1);</span>
<span class="nc" id="L61">            System.out.println(&quot;Counter value: &quot; + counterValue);</span>

<span class="nc" id="L63">            System.out.println(&quot;Incrementing counter by 10&quot;);</span>
<span class="nc" id="L64">            counterValue = client.incrBy(&quot;counter&quot;, 10);</span>
<span class="nc" id="L65">            System.out.println(&quot;Counter value: &quot; + counterValue);</span>

<span class="nc" id="L67">            System.out.println(&quot;Decrementing counter by 5&quot;);</span>
<span class="nc" id="L68">            counterValue = client.decrBy(&quot;counter&quot;, 5);</span>
<span class="nc" id="L69">            System.out.println(&quot;Counter value: &quot; + counterValue);</span>

            // Hash operations
<span class="nc" id="L72">            System.out.println(&quot;Setting hash field&quot;);</span>
<span class="nc" id="L73">            boolean hSetResult = client.hSet(&quot;user:1&quot;, &quot;name&quot;, &quot;John Doe&quot;);</span>
<span class="nc" id="L74">            System.out.println(&quot;HSet result: &quot; + hSetResult);</span>

<span class="nc" id="L76">            client.hSet(&quot;user:1&quot;, &quot;email&quot;, &quot;<EMAIL>&quot;);</span>
<span class="nc" id="L77">            client.hSet(&quot;user:1&quot;, &quot;age&quot;, &quot;30&quot;);</span>

<span class="nc" id="L79">            System.out.println(&quot;Getting hash field&quot;);</span>
<span class="nc" id="L80">            String name = client.hGet(&quot;user:1&quot;, &quot;name&quot;);</span>
<span class="nc" id="L81">            System.out.println(&quot;Name: &quot; + name);</span>

<span class="nc" id="L83">            System.out.println(&quot;Getting all hash fields&quot;);</span>
<span class="nc" id="L84">            Map&lt;String, String&gt; userFields = client.hGetAll(&quot;user:1&quot;);</span>
<span class="nc" id="L85">            System.out.println(&quot;User fields: &quot; + userFields);</span>

<span class="nc" id="L87">            System.out.println(&quot;Incrementing hash field&quot;);</span>
<span class="nc" id="L88">            long age = client.hIncrBy(&quot;user:1&quot;, &quot;age&quot;, 1);</span>
<span class="nc" id="L89">            System.out.println(&quot;New age: &quot; + age);</span>

            // Batch operations
<span class="nc" id="L92">            System.out.println(&quot;Executing batch operations&quot;);</span>
<span class="nc" id="L93">            BatchOperationBuilder batchBuilder = new BatchOperationBuilder()</span>
<span class="nc" id="L94">                    .addSet(&quot;batch1&quot;, &quot;value1&quot;)</span>
<span class="nc" id="L95">                    .addSet(&quot;batch2&quot;, &quot;value2&quot;)</span>
<span class="nc" id="L96">                    .addSetEx(&quot;batch3&quot;, &quot;value3&quot;, 300)</span>
<span class="nc" id="L97">                    .addHSet(&quot;batch:hash&quot;, &quot;field1&quot;, &quot;value1&quot;)</span>
<span class="nc" id="L98">                    .addHSet(&quot;batch:hash&quot;, &quot;field2&quot;, &quot;value2&quot;);</span>

<span class="nc" id="L100">            List&lt;RustyClusterProto.BatchOperation&gt; batchOperations = batchBuilder.build();</span>
<span class="nc" id="L101">            List&lt;Boolean&gt; batchResults = client.batchWrite(batchOperations);</span>
<span class="nc" id="L102">            System.out.println(&quot;Batch results: &quot; + batchResults);</span>

            // Verify batch results
<span class="nc" id="L105">            System.out.println(&quot;Verifying batch results&quot;);</span>
<span class="nc" id="L106">            System.out.println(&quot;batch1: &quot; + client.get(&quot;batch1&quot;));</span>
<span class="nc" id="L107">            System.out.println(&quot;batch2: &quot; + client.get(&quot;batch2&quot;));</span>
<span class="nc" id="L108">            System.out.println(&quot;batch3: &quot; + client.get(&quot;batch3&quot;));</span>
<span class="nc" id="L109">            System.out.println(&quot;batch:hash fields: &quot; + client.hGetAll(&quot;batch:hash&quot;));</span>

            // Clean up
            /*System.out.println(&quot;Cleaning up&quot;);
            client.delete(&quot;greeting&quot;);
            client.delete(&quot;temp&quot;);
            client.delete(&quot;counter&quot;);
            client.delete(&quot;user:1&quot;);
            client.delete(&quot;batch1&quot;);
            client.delete(&quot;batch2&quot;);
            client.delete(&quot;batch3&quot;);
            client.delete(&quot;batch:hash&quot;);*/
<span class="nc" id="L121">        }</span>
<span class="nc" id="L122">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>