<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KeyValueServiceGrpc.KeyValueServiceFutureStub</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">com.rustycluster.grpc</a> &gt; <span class="el_class">KeyValueServiceGrpc.KeyValueServiceFutureStub</span></div><h1>KeyValueServiceGrpc.KeyValueServiceFutureStub</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">164 of 164</td><td class="ctr2">0%</td><td class="bar">0 of 0</td><td class="ctr2">n/a</td><td class="ctr1">19</td><td class="ctr2">19</td><td class="ctr1">37</td><td class="ctr2">37</td><td class="ctr1">19</td><td class="ctr2">19</td></tr></tfoot><tbody><tr><td id="a0"><a href="KeyValueServiceGrpc.java.html#L1090" class="el_method">authenticate(RustyClusterProto.AuthenticateRequest)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"/><td class="ctr2" id="e0">n/a</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g0">1</td><td class="ctr1" id="h0">2</td><td class="ctr2" id="i0">2</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a15"><a href="KeyValueServiceGrpc.java.html#L1101" class="el_method">ping(RustyClusterProto.PingRequest)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">1</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i1">2</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a16"><a href="KeyValueServiceGrpc.java.html#L1112" class="el_method">set(RustyClusterProto.SetRequest)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">2</td><td class="ctr2" id="i2">2</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="KeyValueServiceGrpc.java.html#L1120" class="el_method">get(RustyClusterProto.GetRequest)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">2</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="KeyValueServiceGrpc.java.html#L1128" class="el_method">delete(RustyClusterProto.DeleteRequest)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">2</td><td class="ctr2" id="i4">2</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a17"><a href="KeyValueServiceGrpc.java.html#L1136" class="el_method">setEx(RustyClusterProto.SetExRequest)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">2</td><td class="ctr2" id="i5">2</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a18"><a href="KeyValueServiceGrpc.java.html#L1144" class="el_method">setExpiry(RustyClusterProto.SetExpiryRequest)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">2</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a12"><a href="KeyValueServiceGrpc.java.html#L1155" class="el_method">incrBy(RustyClusterProto.IncrByRequest)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">2</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a3"><a href="KeyValueServiceGrpc.java.html#L1163" class="el_method">decrBy(RustyClusterProto.DecrByRequest)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a13"><a href="KeyValueServiceGrpc.java.html#L1171" class="el_method">incrByFloat(RustyClusterProto.IncrByFloatRequest)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a11"><a href="KeyValueServiceGrpc.java.html#L1182" class="el_method">hSet(RustyClusterProto.HSetRequest)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">2</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a7"><a href="KeyValueServiceGrpc.java.html#L1190" class="el_method">hGet(RustyClusterProto.HGetRequest)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a8"><a href="KeyValueServiceGrpc.java.html#L1198" class="el_method">hGetAll(RustyClusterProto.HGetAllRequest)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a9"><a href="KeyValueServiceGrpc.java.html#L1206" class="el_method">hIncrBy(RustyClusterProto.HIncrByRequest)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a6"><a href="KeyValueServiceGrpc.java.html#L1214" class="el_method">hDecrBy(RustyClusterProto.HDecrByRequest)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a10"><a href="KeyValueServiceGrpc.java.html#L1222" class="el_method">hIncrByFloat(RustyClusterProto.HIncrByFloatRequest)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">2</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a1"><a href="KeyValueServiceGrpc.java.html#L1233" class="el_method">batchWrite(RustyClusterProto.BatchWriteRequest)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">2</td><td class="ctr2" id="i16">2</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a2"><a href="KeyValueServiceGrpc.java.html#L1080" class="el_method">build(Channel, CallOptions)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="6" alt="6"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a14"><a href="KeyValueServiceGrpc.java.html#L1074" class="el_method">KeyValueServiceGrpc.KeyValueServiceFutureStub(Channel, CallOptions)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="5" alt="5"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h17">2</td><td class="ctr2" id="i17">2</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>